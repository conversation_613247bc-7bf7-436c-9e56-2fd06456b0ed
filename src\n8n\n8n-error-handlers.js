/**
 * n8n-specific error handlers for workflow error management
 */

export class N8nErrorHandlers {
  constructor(logger) {
    this.logger = logger;
    this.errorMappings = this.setupErrorMappings();
  }

  /**
   * Setup error mappings for n8n workflows
   */
  setupErrorMappings() {
    return {
      // Input validation errors
      'ValidationError': {
        n8nCode: 'INPUT_VALIDATION_ERROR',
        category: 'input',
        retryable: false,
        userMessage: 'Invalid input data provided to workflow'
      },
      'TypeError': {
        n8nCode: 'TYPE_ERROR',
        category: 'input',
        retryable: false,
        userMessage: 'Incorrect data type provided'
      },
      
      // File system errors
      'ENOENT': {
        n8nCode: 'FILE_NOT_FOUND',
        category: 'filesystem',
        retryable: false,
        userMessage: 'Required file or directory not found'
      },
      'EACCES': {
        n8nCode: 'PERMISSION_DENIED',
        category: 'filesystem',
        retryable: false,
        userMessage: 'Permission denied accessing file or directory'
      },
      'ENOSPC': {
        n8nCode: 'DISK_SPACE_ERROR',
        category: 'filesystem',
        retryable: false,
        userMessage: 'Insufficient disk space for operation'
      },
      
      // Network errors
      'ENOTFOUND': {
        n8nCode: 'NETWORK_ERROR',
        category: 'network',
        retryable: true,
        userMessage: 'Network connection failed'
      },
      'ETIMEDOUT': {
        n8nCode: 'TIMEOUT_ERROR',
        category: 'network',
        retryable: true,
        userMessage: 'Operation timed out'
      },
      
      // Application errors
      'N8N_WORKFLOW_TIMEOUT': {
        n8nCode: 'WORKFLOW_TIMEOUT',
        category: 'workflow',
        retryable: false,
        userMessage: 'Workflow execution exceeded time limit'
      },
      'AUDIT_PATH_ERROR': {
        n8nCode: 'AUDIT_PROCESSING_ERROR',
        category: 'business',
        retryable: false,
        userMessage: 'Error processing audit folder'
      }
    };
  }

  /**
   * Handle n8n tool errors with workflow-friendly formatting
   */
  handleN8nToolError(toolName, error, context) {
    const errorInfo = this.analyzeError(error);
    const n8nError = this.formatErrorForN8n(toolName, error, errorInfo, context);
    
    // Log error for debugging
    this.logN8nError(toolName, error, errorInfo, context);
    
    // Return n8n-formatted error response
    return {
      content: [{
        type: 'text',
        text: JSON.stringify(n8nError, null, 2)
      }],
      isError: true
    };
  }

  /**
   * Analyze error to determine type and handling strategy
   */
  analyzeError(error) {
    const errorType = error.constructor.name;
    const errorCode = error.code || error.name || 'UNKNOWN_ERROR';
    const mapping = this.errorMappings[errorCode] || this.errorMappings[errorType] || this.getDefaultErrorMapping();
    
    return {
      originalType: errorType,
      originalCode: errorCode,
      originalMessage: error.message,
      stack: error.stack,
      mapping,
      severity: this.determineSeverity(error),
      context: this.extractErrorContext(error)
    };
  }

  /**
   * Format error for n8n workflow consumption
   */
  formatErrorForN8n(toolName, error, errorInfo, context) {
    return {
      success: false,
      error: {
        // n8n workflow error structure
        n8nCode: errorInfo.mapping.n8nCode,
        category: errorInfo.mapping.category,
        severity: errorInfo.severity,
        message: errorInfo.mapping.userMessage,
        retryable: errorInfo.mapping.retryable,
        
        // Detailed error information
        details: {
          tool: toolName,
          originalError: errorInfo.originalMessage,
          originalType: errorInfo.originalType,
          originalCode: errorInfo.originalCode,
          executionId: context.executionId,
          executionTime: context.executionTime
        },
        
        // n8n workflow guidance
        n8nGuidance: {
          suggestedAction: this.getSuggestedAction(errorInfo),
          workflowTips: this.getWorkflowTips(errorInfo, toolName),
          retryStrategy: this.getRetryStrategy(errorInfo),
          errorHandling: this.getErrorHandlingTips(errorInfo)
        },
        
        // Context information
        context: {
          timestamp: new Date().toISOString(),
          args: this.sanitizeArgsForLogging(context.args),
          environment: process.env.NODE_ENV || 'unknown'
        }
      },
      
      // n8n metadata for workflow processing
      n8nMetadata: {
        errorId: this.generateErrorId(),
        canRetry: errorInfo.mapping.retryable,
        failWorkflow: errorInfo.severity === 'critical',
        logLevel: this.getLogLevel(errorInfo.severity),
        notifyUser: errorInfo.severity !== 'low'
      }
    };
  }

  /**
   * Log error with n8n-specific context
   */
  logN8nError(toolName, error, errorInfo, context) {
    const logData = {
      type: 'n8n_workflow_error',
      tool: toolName,
      executionId: context.executionId,
      n8nCode: errorInfo.mapping.n8nCode,
      category: errorInfo.mapping.category,
      severity: errorInfo.severity,
      retryable: errorInfo.mapping.retryable,
      originalError: errorInfo.originalMessage,
      executionTime: context.executionTime,
      timestamp: new Date().toISOString()
    };

    // Log based on severity
    switch (errorInfo.severity) {
      case 'critical':
        this.logger.error('Critical n8n workflow error:', logData);
        break;
      case 'high':
        this.logger.error('High severity n8n workflow error:', logData);
        break;
      case 'medium':
        this.logger.warn('Medium severity n8n workflow error:', logData);
        break;
      case 'low':
        this.logger.info('Low severity n8n workflow error:', logData);
        break;
      default:
        this.logger.error('Unknown severity n8n workflow error:', logData);
    }
  }

  /**
   * Handle critical errors that could crash the server
   */
  handleCriticalError(error) {
    this.logger.error('Critical error in n8n MCP server:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      action: 'server_shutdown_initiated'
    });

    // Send critical error notification to n8n if possible
    this.notifyN8nCriticalError(error);
  }

  /**
   * Handle promise rejections
   */
  handlePromiseRejection(reason, promise) {
    this.logger.error('Unhandled promise rejection in n8n server:', {
      reason: reason?.message || reason,
      stack: reason?.stack,
      promise: promise.toString(),
      timestamp: new Date().toISOString()
    });

    // Convert to trackable error
    const error = reason instanceof Error ? reason : new Error(String(reason));
    this.handleCriticalError(error);
  }

  /**
   * Handle startup errors
   */
  async handleStartupError(error) {
    this.logger.error('n8n MCP server startup failed:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // Send startup failure notification to n8n
    this.notifyN8nStartupFailure(error);
  }

  /**
   * Determine error severity
   */
  determineSeverity(error) {
    // Critical errors that prevent operation
    if (error.code === 'ENOSPC' || error.message.includes('out of memory')) {
      return 'critical';
    }
    
    // High severity - data loss risk
    if (error.message.includes('delete') || error.message.includes('corrupt')) {
      return 'high';
    }
    
    // Medium severity - operation failures
    if (error.code === 'ENOENT' || error.code === 'EACCES') {
      return 'medium';
    }
    
    // Low severity - validation or temporary issues
    if (error.name === 'ValidationError' || error.code === 'ETIMEDOUT') {
      return 'low';
    }
    
    return 'medium'; // Default
  }

  /**
   * Extract error context information
   */
  extractErrorContext(error) {
    const context = {};
    
    // Extract file path if present
    if (error.path) {
      context.filePath = error.path;
    }
    
    // Extract syscall information
    if (error.syscall) {
      context.syscall = error.syscall;
    }
    
    // Extract errno
    if (error.errno) {
      context.errno = error.errno;
    }
    
    return context;
  }

  /**
   * Get default error mapping for unknown errors
   */
  getDefaultErrorMapping() {
    return {
      n8nCode: 'UNKNOWN_ERROR',
      category: 'unknown',
      retryable: false,
      userMessage: 'An unexpected error occurred'
    };
  }

  /**
   * Get suggested action for error
   */
  getSuggestedAction(errorInfo) {
    switch (errorInfo.mapping.category) {
      case 'input':
        return 'Check and correct the input data format';
      case 'filesystem':
        return 'Verify file paths and permissions';
      case 'network':
        return 'Check network connectivity and retry';
      case 'workflow':
        return 'Adjust workflow timeout settings';
      case 'business':
        return 'Review audit folder configuration';
      default:
        return 'Review error details and contact support if needed';
    }
  }

  /**
   * Get workflow tips for error handling
   */
  getWorkflowTips(errorInfo, toolName) {
    const tips = [];
    
    if (errorInfo.mapping.retryable) {
      tips.push('Add a "Retry" node after this operation');
    }
    
    if (errorInfo.mapping.category === 'input') {
      tips.push('Add input validation before this node');
    }
    
    if (errorInfo.mapping.category === 'filesystem') {
      tips.push('Add file existence check before processing');
    }
    
    if (toolName === 'copy_audit_folders') {
      tips.push('Consider using smaller batch sizes for copy operations');
    }
    
    return tips;
  }

  /**
   * Get retry strategy
   */
  getRetryStrategy(errorInfo) {
    if (!errorInfo.mapping.retryable) {
      return 'Do not retry - fix the underlying issue first';
    }
    
    switch (errorInfo.mapping.category) {
      case 'network':
        return 'Exponential backoff: 1s, 2s, 4s, 8s';
      case 'filesystem':
        return 'Linear retry: 5s intervals, max 3 attempts';
      default:
        return 'Simple retry: 2s interval, max 2 attempts';
    }
  }

  /**
   * Get error handling tips
   */
  getErrorHandlingTips(errorInfo) {
    const tips = [];
    
    tips.push('Use "If" node to check for error conditions');
    
    if (errorInfo.severity === 'critical') {
      tips.push('Add immediate workflow termination');
      tips.push('Send error notification to administrators');
    }
    
    if (errorInfo.mapping.category === 'business') {
      tips.push('Log error details for audit trail');
    }
    
    return tips;
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return `n8n_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get log level for severity
   */
  getLogLevel(severity) {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warn';
      case 'low':
        return 'info';
      default:
        return 'error';
    }
  }

  /**
   * Sanitize arguments for logging (remove sensitive data)
   */
  sanitizeArgsForLogging(args) {
    if (!args) return {};
    
    const sanitized = { ...args };
    
    // Remove or mask sensitive fields
    if (sanitized.sessionId) {
      sanitized.sessionId = sanitized.sessionId.substr(0, 8) + '...';
    }
    
    if (sanitized.n8nContext?.workflowId) {
      sanitized.n8nContext.workflowId = sanitized.n8nContext.workflowId.substr(0, 8) + '...';
    }
    
    return sanitized;
  }

  /**
   * Notify n8n of critical errors
   */
  notifyN8nCriticalError(error) {
    try {
      if (process.env.N8N_CONTEXT) {
        console.log(JSON.stringify({
          type: 'n8n_critical_error',
          error: error.message,
          timestamp: new Date().toISOString(),
          action: 'server_restart_required'
        }));
      }
    } catch (notificationError) {
      this.logger.error('Failed to notify n8n of critical error:', notificationError);
    }
  }

  /**
   * Notify n8n of startup failures
   */
  notifyN8nStartupFailure(error) {
    try {
      if (process.env.N8N_CONTEXT) {
        console.log(JSON.stringify({
          type: 'n8n_startup_failure',
          error: error.message,
          timestamp: new Date().toISOString(),
          action: 'check_configuration'
        }));
      }
    } catch (notificationError) {
      // Can't log this since logger might not be available
      console.error('Failed to notify n8n of startup failure:', notificationError);
    }
  }

  /**
   * Create user-friendly error message for n8n UI
   */
  createUserFriendlyMessage(errorInfo, toolName) {
    const baseMessage = errorInfo.mapping.userMessage;
    
    switch (toolName) {
      case 'analyze_audit_paths':
        return `Analysis failed: ${baseMessage}. Check audit folder names and permissions.`;
      case 'copy_audit_folders':
        return `Copy operation failed: ${baseMessage}. Verify source and target locations.`;
      case 'generate_long_names_report':
        return `Report generation failed: ${baseMessage}. Check folder access permissions.`;
      case 'get_operation_status':
        return `Status check failed: ${baseMessage}. Operation may have completed or failed.`;
      default:
        return `Operation failed: ${baseMessage}`;
    }
  }

  /**
   * Format validation errors specifically for n8n
   */
  formatValidationError(validationError, toolName) {
    return {
      n8nCode: 'INPUT_VALIDATION_ERROR',
      category: 'input',
      severity: 'low',
      message: `Invalid input for ${toolName}`,
      details: {
        field: validationError.field || 'unknown',
        value: validationError.value || 'unknown',
        expected: validationError.expected || 'valid format',
        provided: validationError.provided || 'invalid format'
      },
      n8nGuidance: {
        suggestedAction: 'Correct the input format and try again',
        workflowTips: ['Add input validation node before this operation'],
        retryStrategy: 'Do not retry - fix input first'
      }
    };
  }
}
