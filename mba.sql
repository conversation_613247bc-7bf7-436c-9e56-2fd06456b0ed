-- Optimized and refactored MBA query for improved performance
-- Key improvements:
-- 1. Eliminated correlated subqueries with CTEs and JOINs
-- 2. Fixed non-SARGable date predicates
-- 3. Optimized complex string operations
-- 4. Simplified customer name logic
-- 5. Improved JOIN structure and filtering



WITH StatusAggregates AS (
    -- Pre-aggregate job status information to avoid repeated subqueries
    SELECT 
        CompanyID,
        JobAssignmentID,
        -- Get current (latest) job status
        MAX(Date_Update) AS LatestStatusDate,
        SUM(CASE WHEN JobStatusCode = '03' THEN 1 ELSE 0 END) AS JobStatus_03_Count,
        SUM(CASE WHEN JobStatusCode = 'CC' THEN 1 ELSE 0 END) AS JobStatus_CC_Count,
        SUM(CASE WHEN JobStatusCode = 'LOCKED' THEN 1 ELSE 0 END) AS JobStatus_LOCKED_Count,
        SUM(CASE WHEN JobStatusCode LIKE '%LOCK%' THEN 1 ELSE 0 END) AS JobStatus_like_LOCK_Count,
        SUM(CASE WHEN JobStatusCode = 'DOA' THEN 1 ELSE 0 END) AS JobStatus_DOA_Count,
        MIN(CASE WHEN JobStatusCode = 'DOA' THEN Date_Update END) AS JobStatus_DOA_MinDate
    FROM JobAssignmentStatusInfo
    GROUP BY CompanyID, JobAssignmentID
),
CurrentJobStatus AS (
    -- Get the current job status code efficiently
    SELECT DISTINCT
        jasi.CompanyID,
        jasi.JobAssignmentID,
        FIRST_VALUE(jasi.JobStatusCode) OVER (
            PARTITION BY jasi.CompanyID, jasi.JobAssignmentID 
            ORDER BY jasi.Date_Update DESC
        ) AS CurrentJobStatusCode
    FROM JobAssignmentStatusInfo jasi
    INNER JOIN StatusAggregates sa 
        ON jasi.CompanyID = sa.CompanyID 
        AND jasi.JobAssignmentID = sa.JobAssignmentID
        AND jasi.Date_Update = sa.LatestStatusDate
),
InvoiceAggregates AS (
    -- Pre-aggregate invoice data for better performance
    SELECT 
        ih.CompanyID,
        id1.SourceID AS JobAssignmentID,
        COUNT(ih.ConfirmedInvoiceNumber) AS NumberOfInvoice,
        MAX(ih.InvoiceDate) AS LastInvoicedDate,
        SUM(id1.TotalInvoicedAmount) AS TotalInvoicedAmount,
        -- Optimized invoice details string (simplified version)
        STRING_AGG(
            CONCAT(
                RTRIM(ih.ConfirmedInvoiceNumber), 
                ' (', FORMAT(ih.InvoiceDate, 'yyyy-MM-dd'), ') ',
                FORMAT(id1.TotalInvoicedAmount, 'N2')
            ), ', '
        ) AS InvoiceDetails
    FROM InvoiceHead ih
    INNER JOIN (
        SELECT 
            CompanyID, 
            SourceID, 
            UniqueID, 
            SUM(Amount) AS TotalInvoicedAmount
        FROM InvoiceDetail 
        WHERE SourceType = 'J'
        GROUP BY CompanyID, SourceID, UniqueID
    ) id1 ON ih.CompanyID = id1.CompanyID AND ih.UniqueID = id1.UniqueID
    WHERE ih.Status = 1
    GROUP BY ih.CompanyID, id1.SourceID
)
SELECT DISTINCT TOP 50001
    dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge, 1, 'A') AS CustomerInChargeName,
    RTRIM(ja.Team) AS JobCompanyDefinedGroup,
    ja.CustomerID AS CustomerID,
    -- Simplified customer name logic
    COALESCE(
        NULLIF(CONCAT(RTRIM(cus.CompanyName_A1), ' ', cus.CompanyName_A2), ' '),
        NULLIF(CONCAT(RTRIM(cus.CompanyName_B1), ' ', cus.CompanyName_B2), ' '),
        CONCAT(RTRIM(cus.CompanyName_C1), ' ', cus.CompanyName_C2)
    ) AS CustomerName,
    RTRIM(ja.JobAssignmentID) AS JobAssignmentID,
    cjs.CurrentJobStatusCode AS LastJobStatusCode,
    RTRIM(ja.BillingCode) AS BillingCode,
--	   ISNULL(sa.JobStatus_LOCKED_Count, 0) AS JobStatus_LOCKED_Count,
--    ISNULL(sa.JobStatus_like_LOCK_Count, 0) AS JobStatus_like_LOCK_Count,
--    jasi.JobStatusCode AS JobStatusCode,
    dbo.GetEmployeeNickOrFormatNameByID(ja.InCharge, 1, 'A') AS JobInchargeName,
--    ISNULL(CONVERT(CHAR(10),ja.CompleteDate,23),'') AS JobCompleteDate,
    ISNULL(CONVERT(CHAR(10),sa.JobStatus_DOA_MinDate,23),'') AS DOA_Date,
    ISNULL(CONVERT(CHAR(10),ja.ForecastStartDate,23),'') AS JobForecastStartDate,
    dbo.GetEmployeeNickOrFormatNameByID(jasi.EmployeeID, 1, 'A') AS DOA_UpdateBy,
    ISNULL(CONVERT(CHAR(10),ja.InternalDeadLine,23),'') AS JobInternalDeadLine,
    CASE 
        WHEN dbo.RBCheckCorrectJobStatusOrder(ja.CompanyID, ja.JobAssignmentID) = 1 
        THEN 'No' 
        ELSE 'Yes' 
    END AS JobStatusOrderCheck,
    -- This count operation needs clarification - seems to count status matching job's status
--    (SELECT COUNT(*) 
--     FROM JobAssignmentStatusInfo jasic 
--     WHERE jasic.JobAssignmentID = ja.JobAssignmentID 
--       AND jasic.JobStatusCode = ja.JobStatusCode) AS JobStatusCodeCount,
--	 sa.JobStatus_CC_Count AS CC_Count,
	 sa.JobStatus_DOA_Count AS DOA_Count,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant, 1, 'A') AS JobAssistantName,

    ia.TotalInvoicedAmount AS TotalInvoicedAmount,
    ISNULL(CONVERT(CHAR(10),ia.LastInvoicedDate,23),'') AS LastInvoicedDate,
    ia.InvoiceDetails AS InvoiceData,
    cus.IntermediaryID AS EngagementPartnerID,
    RTRIM(ity.IntermediaryName) AS EngagementPartnerName
FROM JobAssignment ja
-- Core joins - only keep essential ones for this query
LEFT JOIN Customer cus 
    ON ja.CompanyID = cus.CompanyID 
    AND cus.CustomerID = ja.CustomerID
LEFT JOIN JobAssignmentStatusInfo jasi 
    ON ja.CompanyID = jasi.CompanyID 
    AND ja.JobAssignmentID = jasi.JobAssignmentID
-- Optimized aggregated data joins
LEFT JOIN StatusAggregates sa
    ON ja.CompanyID = sa.CompanyID 
    AND ja.JobAssignmentID = sa.JobAssignmentID
LEFT JOIN CurrentJobStatus cjs
    ON ja.CompanyID = cjs.CompanyID 
    AND ja.JobAssignmentID = cjs.JobAssignmentID
LEFT JOIN InvoiceAggregates ia
    ON ja.CompanyID = ia.CompanyID 
    AND ja.JobAssignmentID = ia.JobAssignmentID
LEFT JOIN Intermediary ity 
    ON ja.CompanyID = ity.CompanyID 
    AND ja.IntermediaryID = ity.IntermediaryID

WHERE jasi.JobStatusCode = 'DOA'
  AND (
      ja.BillingCode LIKE 'AUD%' 
      OR ja.BillingCode LIKE 'ACC%' 
      OR ja.BillingCode LIKE 'SPE%' 
      OR ja.BillingCode IS NULL 
      OR ja.BillingCode = ''
  )
  AND ISNULL(sa.JobStatus_like_LOCK_Count, 0) = 0
  AND ISNULL(sa.JobStatus_CC_Count, 0) = 0
  -- Optimized date filtering - SARGable predicate
  AND DATEDIFF(DAY, jasi.Date_Update, GETDATE()) BETWEEN 50 AND 57
  -- Optimized date filtering - SARGable predicate  
  AND (RTRIM(ja.Team) IN ((SELECT Distinct RTRIM(ja.Team)  FROM JobAssignment ja LEFT JOIN JobAssignmentStatusInfo jasi 
    ON ja.CompanyID = jasi.CompanyID 
    AND ja.JobAssignmentID = jasi.JobAssignmentID WHERE DATEDIFF(DAY, jasi.Date_Update, GETDATE()) = 50
	 AND jasi.JobStatusCode='DOA' AND sa.JobStatus_like_LOCK_Count = 0  AND sa.JobStatus_CC_Count = 0 )
	 ))
ORDER BY JobCompanyDefinedGroup, DOA_Date