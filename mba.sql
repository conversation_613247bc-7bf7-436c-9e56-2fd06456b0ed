-- ULTRA HIGH-PERFORMANCE MBA QUERY - COMPLETELY OPTIMIZED
-- Major performance improvements:
-- 1. Early filtering with date range calculations
-- 2. Eliminated expensive DATEDIFF functions in WHERE clause
-- 3. Removed correlated subquery in WHERE clause
-- 4. Optimized CTEs with proper indexing strategy
-- 5. Reduced function calls and string operations
-- 6. Fixed SARGable predicates for index usage

-- CRITICAL INDEXES REQUIRED FOR OPTIMAL PERFORMANCE:
-- CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_DOA_Performance ON JobAssignmentStatusInfo (JobStatusCode, Date_Update) INCLUDE (CompanyID, JobAssignmentID, EmployeeID) WHERE JobStatusCode = 'DOA';
-- CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_Status_Performance ON JobAssignmentStatusInfo (CompanyID, JobAssignmentID) INCLUDE (JobStatusCode, Date_Update, EmployeeID);
-- CREATE NONCLUSTERED INDEX IX_JobAssignment_Performance ON JobAssignment (CompanyID, JobAssignmentID, BillingCode) INCLUDE (Team, CustomerID, InCharge, Assistant, ForecastStartDate, InternalDeadLine, IntermediaryID);

WITH DateRange AS (
    -- Pre-calculate date range for SARGable predicates
    SELECT
        DATEADD(DAY, -57, CAST(GETDATE() AS DATE)) AS StartDate,
        DATEADD(DAY, -50, CAST(GETDATE() AS DATE)) AS EndDate,
        DATEADD(DAY, -50, CAST(GETDATE() AS DATE)) AS ExactDate
),

DOAJobsFiltered AS (
    -- Start with most restrictive filter - DOA jobs in date range with business logic
    SELECT DISTINCT
        jasi.CompanyID,
        jasi.JobAssignmentID,
        jasi.JobStatusCode,
        jasi.Date_Update,
        jasi.EmployeeID
    FROM JobAssignmentStatusInfo jasi
    CROSS JOIN DateRange dr
    INNER JOIN JobAssignment ja
        ON jasi.CompanyID = ja.CompanyID
        AND jasi.JobAssignmentID = ja.JobAssignmentID
        -- Apply business filters early
        AND (ja.BillingCode LIKE 'AUD%' OR ja.BillingCode LIKE 'ACC%' OR ja.BillingCode LIKE 'SPE%'
             OR ja.BillingCode IS NULL OR ja.BillingCode = '')
    WHERE jasi.JobStatusCode = 'DOA'
        AND CAST(jasi.Date_Update AS DATE) BETWEEN dr.StartDate AND dr.EndDate
),

StatusAggregated AS (
    -- Efficient status aggregation for filtered jobs only
    SELECT
        jsi.CompanyID,
        jsi.JobAssignmentID,
        MAX(jsi.Date_Update) AS LatestStatusDate,
        SUM(CASE WHEN jsi.JobStatusCode = 'CC' THEN 1 ELSE 0 END) AS JobStatus_CC_Count,
        SUM(CASE WHEN jsi.JobStatusCode LIKE '%LOCK%' THEN 1 ELSE 0 END) AS JobStatus_LOCK_Count,
        SUM(CASE WHEN jsi.JobStatusCode = 'DOA' THEN 1 ELSE 0 END) AS JobStatus_DOA_Count,
        MIN(CASE WHEN jsi.JobStatusCode = 'DOA' THEN jsi.Date_Update END) AS JobStatus_DOA_MinDate,
        -- Get latest status efficiently
        (SELECT TOP 1 JobStatusCode
         FROM JobAssignmentStatusInfo jsi2
         WHERE jsi2.CompanyID = jsi.CompanyID
           AND jsi2.JobAssignmentID = jsi.JobAssignmentID
         ORDER BY jsi2.Date_Update DESC) AS CurrentJobStatusCode
    FROM JobAssignmentStatusInfo jsi
    INNER JOIN DOAJobsFiltered dj
        ON jsi.CompanyID = dj.CompanyID
        AND jsi.JobAssignmentID = dj.JobAssignmentID
    GROUP BY jsi.CompanyID, jsi.JobAssignmentID
),

TeamFilter AS (
    -- Pre-calculate team filter efficiently
    SELECT DISTINCT RTRIM(ja.Team) AS Team
    FROM DOAJobsFiltered dj
    INNER JOIN JobAssignment ja
        ON dj.CompanyID = ja.CompanyID
        AND dj.JobAssignmentID = ja.JobAssignmentID
    INNER JOIN StatusAggregated sa
        ON dj.CompanyID = sa.CompanyID
        AND dj.JobAssignmentID = sa.JobAssignmentID
    CROSS JOIN DateRange dr
    WHERE CAST(dj.Date_Update AS DATE) = dr.ExactDate
        AND sa.JobStatus_LOCK_Count = 0
        AND sa.JobStatus_CC_Count = 0
),
InvoiceAggregated AS (
    -- Optimized invoice aggregation for filtered jobs only
    SELECT
        dj.CompanyID,
        dj.JobAssignmentID,
        COUNT(ih.ConfirmedInvoiceNumber) AS NumberOfInvoice,
        MAX(ih.InvoiceDate) AS LastInvoicedDate,
        SUM(id.Amount) AS TotalInvoicedAmount,
        STRING_AGG(RTRIM(ih.ConfirmedInvoiceNumber), ', ') AS InvoiceDetails
    FROM DOAJobsFiltered dj
    INNER JOIN InvoiceDetail id
        ON dj.CompanyID = id.CompanyID
        AND dj.JobAssignmentID = id.SourceID
        AND id.SourceType = 'J'
    INNER JOIN InvoiceHead ih
        ON id.CompanyID = ih.CompanyID
        AND id.UniqueID = ih.UniqueID
        AND ih.Status = 1
        AND ih.ConfirmedInvoiceNumber IS NOT NULL
        AND ih.ConfirmedInvoiceNumber <> ''
    GROUP BY dj.CompanyID, dj.JobAssignmentID
)
-- Main ultra-optimized query
SELECT DISTINCT TOP 50001
    dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge, 1, 'A') AS CustomerInChargeName,
    RTRIM(ja.Team) AS JobCompanyDefinedGroup,
    ja.CustomerID AS CustomerID,
    COALESCE(
        NULLIF(CONCAT(RTRIM(cus.CompanyName_A1), ' ', cus.CompanyName_A2), ' '),
        NULLIF(CONCAT(RTRIM(cus.CompanyName_B1), ' ', cus.CompanyName_B2), ' '),
        CONCAT(RTRIM(cus.CompanyName_C1), ' ', cus.CompanyName_C2)
    ) AS CustomerName,
    RTRIM(ja.JobAssignmentID) AS JobAssignmentID,
    sa.CurrentJobStatusCode AS LastJobStatusCode,
    RTRIM(ja.BillingCode) AS BillingCode,
    dbo.GetEmployeeNickOrFormatNameByID(ja.InCharge, 1, 'A') AS JobInchargeName,
    ISNULL(CONVERT(CHAR(10), sa.JobStatus_DOA_MinDate, 23), '') AS DOA_Date,
    ISNULL(CONVERT(CHAR(10), ja.ForecastStartDate, 23), '') AS JobForecastStartDate,
    dbo.GetEmployeeNickOrFormatNameByID(dj.EmployeeID, 1, 'A') AS DOA_UpdateBy,
    ISNULL(CONVERT(CHAR(10), ja.InternalDeadLine, 23), '') AS JobInternalDeadLine,
    CASE
        WHEN dbo.RBCheckCorrectJobStatusOrder(ja.CompanyID, ja.JobAssignmentID) = 1
        THEN 'No'
        ELSE 'Yes'
    END AS JobStatusOrderCheck,
    sa.JobStatus_DOA_Count AS DOA_Count,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant, 1, 'A') AS JobAssistantName,
    ISNULL(ia.TotalInvoicedAmount, 0) AS TotalInvoicedAmount,
    ISNULL(CONVERT(CHAR(10), ia.LastInvoicedDate, 23), '') AS LastInvoicedDate,
    ISNULL(ia.InvoiceDetails, '') AS InvoiceData,
    cus.IntermediaryID AS EngagementPartnerID,
    RTRIM(ity.IntermediaryName) AS EngagementPartnerName

FROM DOAJobsFiltered dj
INNER JOIN JobAssignment ja
    ON dj.CompanyID = ja.CompanyID
    AND dj.JobAssignmentID = ja.JobAssignmentID
INNER JOIN StatusAggregated sa
    ON dj.CompanyID = sa.CompanyID
    AND dj.JobAssignmentID = sa.JobAssignmentID
INNER JOIN TeamFilter tf
    ON RTRIM(ja.Team) = tf.Team
LEFT JOIN Customer cus
    ON ja.CompanyID = cus.CompanyID
    AND ja.CustomerID = cus.CustomerID
LEFT JOIN InvoiceAggregated ia
    ON dj.CompanyID = ia.CompanyID
    AND dj.JobAssignmentID = ia.JobAssignmentID
LEFT JOIN Intermediary ity
    ON ja.CompanyID = ity.CompanyID
    AND ja.IntermediaryID = ity.IntermediaryID

WHERE sa.JobStatus_LOCK_Count = 0
    AND sa.JobStatus_CC_Count = 0

ORDER BY
    RTRIM(ja.Team),
    sa.JobStatus_DOA_MinDate

OPTION (RECOMPILE, MAXDOP 4);