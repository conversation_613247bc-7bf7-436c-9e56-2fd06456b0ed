SELECT DISTINCT 
 	dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge,1,'A') AS InChargeOfCustomer , 
    rtrim(ja.Team) AS JobTeam , 
    rtrim(ja.CustomerID) AS CustomerID , 
	case when cus.CompanyName_A1<>'' then rtrim(cus.CompanyName_A1)+' '+cus.CompanyName_A2 when cus.CompanyName_B1 <>'' then rtrim(cus.CompanyName_B1)+' '+cus.CompanyName_B2 else rtrim(cus.CompanyName_C1)+' '+cus.CompanyName_C2 end AS CustomerName , ja.JobAssignmentID AS JobAssignmentID , 
	CASE 
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='N_LOCK_CNR' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN 
		   'N_LOCK_CNR'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='LOCKED' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN 
		   'LOCKED'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN
		   'LOCKED_F'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN
		   'CC'
		ELSE
			 (select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID and sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
			)  
	end AS CurrentJobStatusCode , 
	rtrim(jasi.JobStatusCode) AS JobStatusCode , 
	cast(ja.CompleteDate as date ) AS JobCompleteDate , 
	cast(jasi.Date_Update as date ) AS JobStatusUpdatedOn , 
	dbo.GetEmployeeNickOrFormatNameByID(jasi.EmployeeID,1,'A') AS JobStatusUpdateBy , 
	cast(ja.InternalDeadLine as date ) AS JobInternalDeadLine , 
	case dbo.RBCheckCorrectJobStatusOrder( ja.CompanyID,ja.JobAssignmentID ) when 1 then 'No' else 'Yes' end AS JobStatusOrderCheck , 
	( select COUNT(*) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = jasi.JobStatusCode ) AS JobStatusCodeCount , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant,1,'A') AS JobAssistantName , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge,1,'A') AS JobIncharge , 
	isnull(jis.NumberOfInvoice, 0) AS NumberOfInvoice , 
	(select replace(replace(( select distinct '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' from InvoiceHead ih, InvoiceDetail id where ih.CompanyID = id.CompanyID and ih.UniqueID = id.UniqueID and ih.Status = 1 and id.SourceType = 'J' and id.CompanyID = ja.CompanyID and id.SourceID = ja.JobAssignmentID and isnull( ih.ConfirmedInvoiceNumber, '' ) <> '' order by '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' for xml path('')),'~~',', '),'~','')) AS JobAssignmentInvoiceList , 
	cast(jis.LastInvoicedDate as date) AS LastInvoicedDate , 
	jis.TotalInvoicedAmount AS TotalInvoicedAmount , 
	( select count(*) from JobAssignmentStatusInfo ssjasi where ssjasi.CompanyID = ja.CompanyID and ssjasi.JobAssignmentID = ja.JobAssignmentID and ssjasi.JobStatusCode = 'DOA' ) AS JobStatus_DOA_Count FROM JobAssignment ja 
LEFT JOIN JobAssignmentMember jam ON jam.CompanyID = ja.CompanyID AND jam.JobAssignmentID = ja.JobAssignmentID 
LEFT JOIN Employee e ON jam.CompanyID = e.CompanyID AND jam.EmployeeID = e.EmployeeID 
LEFT JOIN EmployeeJOb jamj ON e.CompanyID = jamj.CompanyID AND e.EmployeeID = jamj.EmployeeID and ( jamj.Effective = ( SELECT MAX( Effective ) from EmployeeJob jamjs where jamjs.CompanyID = jamj.CompanyID AND jamjs.EmployeeID = jamj.EmployeeID and jamjs.Effective <= GETDATE() )) 
LEFT JOIN TimeCost jamtc ON jamj.CompanyID = jamtc.CompanyID and jamj.TimeCostID = jamtc.TimeCostID and ( jamtc.Effective = ( SELECT MAX( Effective ) from TimeCost jamtcs where jamtcs.CompanyID = jamtc.CompanyID AND jamtcs.TimeCostID = jamtc.TimeCostID and jamtcs.Effective <= GETDATE() )) 
LEFT JOIN Contact c ON jam.CompanyID = c.CompanyID AND e.EmployeeID = c.MasterID AND c.MasterType = 'EMP' AND c.SeqID = 1 
LEFT JOIN Customer cus ON ja.CompanyID = cus.CompanyID AND cus.CustomerID = ja.CustomerID 
LEFT JOIN Contact cc ON ja.CompanyID = cc.CompanyID AND ja.CustomerID = cc.MasterID AND cc.MasterType = 'CUG' AND cc.SeqID = 1 
LEFT JOIN JobAssignmentStatusInfo jasi ON ja.CompanyID = jasi.CompanyID and ja.JobAssignmentID = jasi.JobAssignmentID 
left join Intermediary ity on ja.CompanyID = ity.CompanyID and ja.IntermediaryID = ity.IntermediaryID 
left join CompanyType ct on ity.CompanyID = ct.CompanyID and ity.CompanyType = ct.CompanyType 
left join Contact city  on ity.CompanyID = city.CompanyID and ity.IntermediaryID = city.MasterID and city.MasterType = 'REF' 
left join Industry ind on ity.CompanyID = ind.CompanyID and ity.IndustryID = ind.IndustryID 
left join CodeDefinition cdtitle on city.CompanyID = cdtitle.CompanyID and city.TitleIDCode = cdtitle.Code and GroupCode = 'TITLE' 
LEFT JOIN JobAssignmentUserField jauf on ja.CompanyID = jauf.CompanyID and ja.JobAssignmentID = jauf.JobAssignmentID 
LEFT JOIN ( select ih.CompanyID, id1.SourceID, count( ih.ConfirmedInvoiceNumber ) NumberOfInvoice, max( ih.InvoiceDate ) LastInvoicedDate, sum(id1.TotalInvoicedAmount) TotalInvoicedAmount from InvoiceHead ih 
left join ( select CompanyID, SourceID, UniqueID, sum(Amount) TotalInvoicedAmount from InvoiceDetail where SourceType = 'J' group by CompanyID, SourceID, UniqueID ) id1 
	on ih.CompanyID = id1.CompanyID and ih.UniqueID = id1.UniqueID where ih.Status = 1 group by ih.CompanyID, id1.SourceID ) jis 
	on jis.CompanyID = ja.CompanyID and jis.SourceID = ja.JobAssignmentID WHERE 1=1
		AND (
				CASE WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE (sjasia.JobStatusCode='LOCKED' OR sjasia.JobStatusCode='N_LOCK_CNR' OR sjasia.JobStatusCode like 'LOCKED_Z%' OR sjasia.JobStatusCode like 'LOCKED_S%' ) AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL)
					  THEN 
					   'LOCKED'
					  WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					  THEN
					   'LOCKED_F'
					  WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' 		AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					  THEN
					   'CC'
					  ELSE
						 (select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID 	AND sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
						)  
				END		
			) <> 'LOCKED'
		AND (jasi.JobStatusCode = 'DOA') 
		AND 
			(
			(ja.BillingCode like 'ACC-%') OR (ja.BillingCode like 'AUD-%') 
			OR (ja.BillingCode = 'SPE-NT') OR (ja.BillingCode = 'SPE-DD') OR (ja.BillingCode = 'SPE-OS') OR (ja.BillingCode = 'SPE-PGT') 
			OR (ja.BillingCode = 'SPE-SR') OR (ja.BillingCode = 'SPE-ICR') OR (ja.BillingCode = 'SPE-MAR') OR (ja.BillingCode = 'SPE-PRC') 
			)
		AND (
			(ja.CustomerID NOT LIKE '%K') OR (ja.CustomerID LIKE '%SK')
			)
		AND( convert(char(10), cast(jasi.Date_Update as date ),20) BETWEEN '2025-05-01' AND '2025-05-18')
		Order by InChargeOfCustomer , JobTeam , JobIncharge