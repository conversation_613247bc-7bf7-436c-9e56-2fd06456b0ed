-- High-Performance Optimized MBA Query
-- Fixed all syntax errors and optimized for better performance

WITH FilteredDOAJobs AS (
    -- Start with the most restrictive filter first - DOA jobs in date range
    SELECT 
        jasi.CompanyID,
        jasi.JobAssignmentID,
        jasi.JobStatusCode,
        jasi.Date_Update,
        jasi.EmployeeID
    FROM JobAssignmentStatusInfo jasi
    WHERE jasi.JobStatusCode = 'DOA'
        AND CAST(jasi.Date_Update AS DATE) BETWEEN '2025-06-01' AND '2025-12-31'
),

JobStatusSummary AS (
    -- Pre-calculate job status information for filtered jobs
    SELECT 
        fdj.CompanyID,
        fdj.JobAssignmentID,
        -- Current status logic
        CASE 
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND x.JobStatusCode = 'N_LOCK_CNR') THEN 'N_LOCK_CNR'
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND x.JobStatusCode = 'LOCKED') THEN 'LOCKED'
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND x.JobStatusCode = 'LOCKED_F') THEN 'LOCKED_F'
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND x.JobStatusCode = 'CC') THEN 'CC'
            ELSE (SELECT TOP 1 JobStatusCode FROM JobAssignmentStatusInfo y WHERE y.CompanyID = fdj.CompanyID AND y.JobAssignmentID = fdj.JobAssignmentID AND y.JobStatusCode <> '' ORDER BY y.Date_Update DESC)
        END AS CurrentJobStatusCode,
        -- Filter status for WHERE clause
        CASE 
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND (x.JobStatusCode IN ('LOCKED', 'N_LOCK_CNR') OR x.JobStatusCode LIKE 'LOCKED_Z%' OR x.JobStatusCode LIKE 'LOCKED_S%')) THEN 'LOCKED'
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND x.JobStatusCode = 'LOCKED_F') THEN 'LOCKED_F'
            WHEN EXISTS (SELECT 1 FROM JobAssignmentStatusInfo x WHERE x.CompanyID = fdj.CompanyID AND x.JobAssignmentID = fdj.JobAssignmentID AND x.JobStatusCode = 'CC') THEN 'CC'
            ELSE (SELECT TOP 1 JobStatusCode FROM JobAssignmentStatusInfo y WHERE y.CompanyID = fdj.CompanyID AND y.JobAssignmentID = fdj.JobAssignmentID AND y.JobStatusCode <> '' ORDER BY y.Date_Update DESC)
        END AS FilterStatusCode,
        (SELECT COUNT(*) FROM JobAssignmentStatusInfo z WHERE z.CompanyID = fdj.CompanyID AND z.JobAssignmentID = fdj.JobAssignmentID AND z.JobStatusCode = 'DOA') AS DOA_Count
    FROM FilteredDOAJobs fdj
),

InvoiceData AS (
    -- Pre-aggregate invoice information for filtered jobs
    SELECT 
        ih.CompanyID,
        id.SourceID AS JobAssignmentID,
        COUNT(ih.ConfirmedInvoiceNumber) AS NumberOfInvoice,
        MAX(ih.InvoiceDate) AS LastInvoicedDate,
        SUM(id.Amount) AS TotalInvoicedAmount,
        STRING_AGG(RTRIM(ih.ConfirmedInvoiceNumber), ', ') AS InvoiceList
    FROM InvoiceHead ih 
    INNER JOIN InvoiceDetail id ON ih.CompanyID = id.CompanyID AND ih.UniqueID = id.UniqueID
    INNER JOIN FilteredDOAJobs fdj ON ih.CompanyID = fdj.CompanyID AND id.SourceID = fdj.JobAssignmentID
    WHERE ih.Status = 1 
        AND id.SourceType = 'J'
        AND ISNULL(ih.ConfirmedInvoiceNumber, '') <> ''
    GROUP BY ih.CompanyID, id.SourceID
)

-- Main optimized query
SELECT DISTINCT
    dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge, 1, 'A') AS InChargeOfCustomer,
    RTRIM(ja.Team) AS JobTeam,
    RTRIM(ja.CustomerID) AS CustomerID,
    CASE 
        WHEN RTRIM(ISNULL(cus.CompanyName_A1, '')) <> '' 
        THEN RTRIM(cus.CompanyName_A1) + ' ' + ISNULL(cus.CompanyName_A2, '')
        WHEN RTRIM(ISNULL(cus.CompanyName_B1, '')) <> '' 
        THEN RTRIM(cus.CompanyName_B1) + ' ' + ISNULL(cus.CompanyName_B2, '')
        ELSE RTRIM(ISNULL(cus.CompanyName_C1, '')) + ' ' + ISNULL(cus.CompanyName_C2, '')
    END AS CustomerName,
    ja.JobAssignmentID,
    jss.CurrentJobStatusCode,
    RTRIM(fdj.JobStatusCode) AS JobStatusCode,
    CAST(ja.CompleteDate AS DATE) AS JobCompleteDate,
    CAST(fdj.Date_Update AS DATE) AS JobStatusUpdatedOn,
    dbo.GetEmployeeNickOrFormatNameByID(fdj.EmployeeID, 1, 'A') AS JobStatusUpdateBy,
    CAST(ja.InternalDeadLine AS DATE) AS JobInternalDeadLine,
    CASE dbo.RBCheckCorrectJobStatusOrder(ja.CompanyID, ja.JobAssignmentID) 
        WHEN 1 THEN 'No' 
        ELSE 'Yes' 
    END AS JobStatusOrderCheck,
    (SELECT COUNT(*) FROM JobAssignmentStatusInfo jasic 
     WHERE jasic.JobAssignmentID = fdj.JobAssignmentID 
         AND jasic.JobStatusCode = fdj.JobStatusCode) AS JobStatusCodeCount,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant, 1, 'A') AS JobAssistantName,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge, 1, 'A') AS JobIncharge,
    ISNULL(inv.NumberOfInvoice, 0) AS NumberOfInvoice,
    ISNULL(inv.InvoiceList, '') AS JobAssignmentInvoiceList,
    CAST(inv.LastInvoicedDate AS DATE) AS LastInvoicedDate,
    ISNULL(inv.TotalInvoicedAmount, 0) AS TotalInvoicedAmount,
    jss.DOA_Count AS JobStatus_DOA_Count

FROM FilteredDOAJobs fdj
INNER JOIN JobAssignment ja 
    ON fdj.CompanyID = ja.CompanyID 
    AND fdj.JobAssignmentID = ja.JobAssignmentID
INNER JOIN JobStatusSummary jss 
    ON fdj.CompanyID = jss.CompanyID 
    AND fdj.JobAssignmentID = jss.JobAssignmentID
INNER JOIN Customer cus 
    ON ja.CompanyID = cus.CompanyID 
    AND ja.CustomerID = cus.CustomerID
LEFT JOIN InvoiceData inv 
    ON fdj.CompanyID = inv.CompanyID 
    AND fdj.JobAssignmentID = inv.JobAssignmentID
LEFT JOIN JobAssignmentMember jam 
    ON ja.CompanyID = jam.CompanyID 
    AND ja.JobAssignmentID = jam.JobAssignmentID
LEFT JOIN Employee e 
    ON jam.CompanyID = e.CompanyID 
    AND jam.EmployeeID = e.EmployeeID
LEFT JOIN EmployeeJob jamj 
    ON e.CompanyID = jamj.CompanyID 
    AND e.EmployeeID = jamj.EmployeeID 
    AND jamj.Effective = (
        SELECT MAX(Effective) 
        FROM EmployeeJob jamjs 
        WHERE jamjs.CompanyID = jamj.CompanyID 
            AND jamjs.EmployeeID = jamj.EmployeeID 
            AND jamjs.Effective <= GETDATE()
    )
LEFT JOIN TimeCost jamtc 
    ON jamj.CompanyID = jamtc.CompanyID 
    AND jamj.TimeCostID = jamtc.TimeCostID 
    AND jamtc.Effective = (
        SELECT MAX(Effective) 
        FROM TimeCost jamtcs 
        WHERE jamtcs.CompanyID = jamtc.CompanyID 
            AND jamtcs.TimeCostID = jamtc.TimeCostID 
            AND jamtcs.Effective <= GETDATE()
    )
LEFT JOIN Contact c 
    ON jam.CompanyID = c.CompanyID 
    AND e.EmployeeID = c.MasterID 
    AND c.MasterType = 'EMP' 
    AND c.SeqID = 1
LEFT JOIN Contact cc 
    ON ja.CompanyID = cc.CompanyID 
    AND ja.CustomerID = cc.MasterID 
    AND cc.MasterType = 'CUG' 
    AND cc.SeqID = 1
LEFT JOIN Intermediary ity 
    ON ja.CompanyID = ity.CompanyID 
    AND ja.IntermediaryID = ity.IntermediaryID
LEFT JOIN CompanyType ct 
    ON ity.CompanyID = ct.CompanyID 
    AND ity.CompanyType = ct.CompanyType
LEFT JOIN Contact city 
    ON ity.CompanyID = city.CompanyID 
    AND ity.IntermediaryID = city.MasterID 
    AND city.MasterType = 'REF'
LEFT JOIN Industry ind 
    ON ity.CompanyID = ind.CompanyID 
    AND ity.IndustryID = ind.IndustryID
LEFT JOIN CodeDefinition cdtitle 
    ON city.CompanyID = cdtitle.CompanyID 
    AND city.TitleIDCode = cdtitle.Code 
    AND cdtitle.GroupCode = 'TITLE'
LEFT JOIN JobAssignmentUserField jauf 
    ON ja.CompanyID = jauf.CompanyID 
    AND ja.JobAssignmentID = jauf.JobAssignmentID

WHERE jss.FilterStatusCode <> 'LOCKED'
    AND (
        ja.BillingCode LIKE 'ACC-%' OR ja.BillingCode LIKE 'AUD-%' 
        OR ja.BillingCode IN ('SPE-NT', 'SPE-DD', 'SPE-OS', 'SPE-PGT', 'SPE-SR', 'SPE-ICR', 'SPE-MAR', 'SPE-PRC')
    )
    AND (ja.CustomerID NOT LIKE '%K' OR ja.CustomerID LIKE '%SK')

ORDER BY 
    InChargeOfCustomer, 
    JobTeam, 
    JobIncharge;
