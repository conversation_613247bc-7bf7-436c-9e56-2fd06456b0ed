-- ULTRA HIGH-<PERSON><PERSON><PERSON><PERSON><PERSON>NCE MBA QUERY - COMPLETELY REFACTORED
--
-- CRITIC<PERSON> INDEXES REQUIRED FOR OPTIMAL PERFORMANCE:
-- CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_DOA_Ultra ON JobAssignmentStatusInfo (JobStatusCode, Date_Update) INCLUDE (CompanyID, JobAssignmentID, EmployeeID) WHERE JobStatusCode = 'DOA';
-- CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_Status_Ultra ON JobAssignmentStatusInfo (CompanyID, JobAssignmentID) INCLUDE (JobStatusCode, Date_Update);
-- CREATE NONCLUSTERED INDEX IX_JobAssignment_Ultra ON JobAssignment (CompanyID, JobAssignmentID, BillingCode, CustomerID) INCLUDE (Team, CompleteDate, InternalDeadLine, Assistant, Incharge);
-- CREATE NONCLUSTERED INDEX IX_InvoiceDetail_Ultra ON InvoiceDetail (CompanyID, SourceID, SourceType) INCLUDE (UniqueID, Amount) WHERE SourceType = 'J';
-- CREATE NONCLUSTERED INDEX IX_InvoiceHead_Ultra ON InvoiceHead (CompanyID, UniqueID, Status) INCLUDE (ConfirmedInvoiceNumber, InvoiceDate) WHERE Status = 1;

WITH DOAJobs AS (
    -- Start with most restrictive filter - DOA jobs in date range
    SELECT
        jasi.CompanyID,
        jasi.JobAssignmentID,
        jasi.JobStatusCode,
        jasi.Date_Update,
        jasi.EmployeeID
    FROM JobAssignmentStatusInfo jasi
    WHERE jasi.JobStatusCode = 'DOA'
        AND jasi.Date_Update >= '2025-06-01'
        AND jasi.Date_Update < '2025-06-16'  -- More efficient than BETWEEN with CONVERT
),

StatusAggregated AS (
    -- Single pass through status table to get all status information
    SELECT
        dj.CompanyID,
        dj.JobAssignmentID,
        dj.JobStatusCode AS DOAStatusCode,
        dj.Date_Update AS DOADate,
        dj.EmployeeID AS DOAEmployeeID,
        -- Aggregate all status checks in one pass
        MIN(CASE WHEN jsi.JobStatusCode = 'DOA' THEN jsi.Date_Update END) AS JobStatus_DOA_MinDate,
        MAX(CASE WHEN jsi.JobStatusCode = 'N_LOCK_CNR' THEN 1 ELSE 0 END) AS HasNLockCNR,
        MAX(CASE WHEN jsi.JobStatusCode = 'LOCKED' THEN 1 ELSE 0 END) AS HasLocked,
        MAX(CASE WHEN jsi.JobStatusCode = 'LOCKED_F' THEN 1 ELSE 0 END) AS HasLockedF,
        MAX(CASE WHEN jsi.JobStatusCode = 'CC' THEN 1 ELSE 0 END) AS HasCC,
        MAX(CASE WHEN jsi.JobStatusCode IN ('LOCKED', 'N_LOCK_CNR')
                      OR jsi.JobStatusCode LIKE 'LOCKED_Z%'
                      OR jsi.JobStatusCode LIKE 'LOCKED_S%' THEN 1 ELSE 0 END) AS HasAnyLocked,
        COUNT(CASE WHEN jsi.JobStatusCode = 'DOA' THEN 1 END) AS DOACount,
--        COUNT(CASE WHEN jsi.JobStatusCode = dj.JobStatusCode THEN 1 END) AS StatusCodeCount,
        -- Get latest status efficiently
        (SELECT TOP 1 JobStatusCode
         FROM JobAssignmentStatusInfo jsi2
         WHERE jsi2.CompanyID = dj.CompanyID
           AND jsi2.JobAssignmentID = dj.JobAssignmentID
           AND jsi2.JobStatusCode <> ''
         ORDER BY jsi2.Date_Update DESC) AS LatestStatus
    FROM DOAJobs dj
    LEFT JOIN JobAssignmentStatusInfo jsi
        ON dj.CompanyID = jsi.CompanyID
        AND dj.JobAssignmentID = jsi.JobAssignmentID
    GROUP BY dj.CompanyID, dj.JobAssignmentID, dj.JobStatusCode, dj.Date_Update, dj.EmployeeID
),

StatusCalculated AS (
    SELECT
        *,
        -- Calculate current status efficiently
        CASE
            WHEN HasNLockCNR = 1 THEN 'N_LOCK_CNR'
            WHEN HasLocked = 1 THEN 'LOCKED'
            WHEN HasLockedF = 1 THEN 'LOCKED_F'
            WHEN HasCC = 1 THEN 'CC'
            ELSE LatestStatus
        END AS CurrentStatus,
        -- Calculate filter status
        CASE
            WHEN HasAnyLocked = 1 OR HasLockedF = 1 THEN 'LOCKED'
            WHEN HasCC = 1 THEN 'CC'
            ELSE LatestStatus
        END AS FilterStatus
    FROM StatusAggregated
),

InvoiceAggregated AS (
    -- Pre-aggregate invoice data efficiently
    SELECT
        dj.CompanyID,
        dj.JobAssignmentID,
        COUNT(ih.ConfirmedInvoiceNumber) AS NumberOfInvoice,
        MAX(ih.InvoiceDate) AS LastInvoicedDate,
        SUM(id.Amount) AS TotalInvoicedAmount,
        STRING_AGG(RTRIM(ih.ConfirmedInvoiceNumber), ', ') AS InvoiceList
    FROM DOAJobs dj
    INNER JOIN InvoiceDetail id
        ON dj.CompanyID = id.CompanyID
        AND dj.JobAssignmentID = id.SourceID
        AND id.SourceType = 'J'
    INNER JOIN InvoiceHead ih
        ON id.CompanyID = ih.CompanyID
        AND id.UniqueID = ih.UniqueID
        AND ih.Status = 1
        AND ih.ConfirmedInvoiceNumber IS NOT NULL
        AND ih.ConfirmedInvoiceNumber <> ''
    GROUP BY dj.CompanyID, dj.JobAssignmentID
)

-- Main optimized query
SELECT DISTINCT
    dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge, 1, 'A') AS InChargeOfCustomer,
    RTRIM(ja.Team) AS JobTeam,
    RTRIM(ja.CustomerID) AS CustomerID,
    CASE
        WHEN RTRIM(ISNULL(cus.CompanyName_A1, '')) <> ''
        THEN RTRIM(cus.CompanyName_A1) + ' ' + ISNULL(cus.CompanyName_A2, '')
        WHEN RTRIM(ISNULL(cus.CompanyName_B1, '')) <> ''
        THEN RTRIM(cus.CompanyName_B1) + ' ' + ISNULL(cus.CompanyName_B2, '')
        ELSE RTRIM(ISNULL(cus.CompanyName_C1, '')) + ' ' + ISNULL(cus.CompanyName_C2, '')
    END AS CustomerName,
    RTRIM(ja.JobAssignmentID) AS JobAssignmentID,
    sc.CurrentStatus AS CurrentJobStatusCode,
--    RTRIM(sc.DOAStatusCode) AS JobStatusCode,
--    ja.CompleteDate AS JobCompleteDate,
    ISNULL(CONVERT(CHAR(10), sc.JobStatus_DOA_MinDate, 23), '') AS DOADate,
    dbo.GetEmployeeNickOrFormatNameByID(sc.DOAEmployeeID, 1, 'A') AS DOAUpdateBy,
    ISNULL(CONVERT(CHAR(10), ja.InternalDeadLine, 23), '') AS JobInternalDeadLine,
    CASE dbo.RBCheckCorrectJobStatusOrder(ja.CompanyID, ja.JobAssignmentID)
        WHEN 1 THEN 'No' ELSE 'Yes' END AS JobStatusOrderCheck,
    sc.DOACount AS JobStatus_DOA_Count,
--    sc.StatusCodeCount AS JobStatusCodeCount,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant, 1, 'A') AS JobAssistantName,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge, 1, 'A') AS JobIncharge,
    ISNULL(inv.NumberOfInvoice, 0) AS NumberOfInvoice,
    ISNULL(inv.InvoiceList, '') AS JobAssignmentInvoiceList,
    ISNULL(CONVERT(CHAR(10), inv.LastInvoicedDate, 23), '') AS LastInvoicedDate,
    ISNULL(inv.TotalInvoicedAmount, 0) AS TotalInvoicedAmount

FROM StatusCalculated sc
INNER JOIN JobAssignment ja
    ON sc.CompanyID = ja.CompanyID
    AND sc.JobAssignmentID = ja.JobAssignmentID
    -- Apply filters early in JOIN for better performance
    AND (ja.BillingCode LIKE 'ACC-%' OR ja.BillingCode LIKE 'AUD-%'
         OR ja.BillingCode IN ('SPE-NT', 'SPE-DD', 'SPE-OS', 'SPE-PGT', 'SPE-SR', 'SPE-ICR', 'SPE-MAR', 'SPE-PRC'))
    AND (ja.CustomerID NOT LIKE '%K' OR ja.CustomerID LIKE '%SK')
INNER JOIN Customer cus
    ON ja.CompanyID = cus.CompanyID
    AND ja.CustomerID = cus.CustomerID
LEFT JOIN InvoiceAggregated inv
    ON sc.CompanyID = inv.CompanyID
    AND sc.JobAssignmentID = inv.JobAssignmentID

WHERE sc.FilterStatus <> 'LOCKED'

ORDER BY
    dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge, 1, 'A'),
    RTRIM(ja.Team),
    dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge, 1, 'A')
