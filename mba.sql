-- MAXIMUM PERFORMANCE MBA QUERY - ULTRA-OPTIMIZED VERSION
-- Performance improvements: Eliminated subqueries, optimized aggregations, better indexing strategy
--
-- CRITICAL INDEXES FOR MAXIMUM PERFORMANCE (CREATE THESE FIRST):
-- CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_DOA_Performance ON JobAssignmentStatusInfo (JobStatusCode, Date_Update) INCLUDE (CompanyID, JobAssignmentID, EmployeeID) WHERE JobStatusCode = 'DOA';
-- CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_Lookup_Performance ON JobAssignmentStatusInfo (CompanyID, JobAssignmentID, JobStatusCode) INCLUDE (Date_Update, EmployeeID);
-- CREATE NONCLUSTERED INDEX IX_JobAssignment_Performance ON JobAssignment (CompanyID, JobAssignmentID, BillingCode, CustomerID) INCLUDE (Team, InternalDeadLine, Assistant, Incharge);
-- CREATE NONCLUSTERED INDEX IX_Customer_Performance ON Customer (CompanyID, CustomerID) INCLUDE (InCharge, CompanyName_A1, CompanyName_A2, CompanyName_B1, CompanyName_B2, CompanyName_C1, CompanyName_C2);
-- CREATE NONCLUSTERED INDEX IX_InvoiceDetail_Performance ON InvoiceDetail (CompanyID, SourceID, SourceType) INCLUDE (UniqueID, Amount) WHERE SourceType = 'J';
-- CREATE NONCLUSTERED INDEX IX_InvoiceHead_Performance ON InvoiceHead (CompanyID, UniqueID, Status) INCLUDE (ConfirmedInvoiceNumber, InvoiceDate) WHERE Status = 1 AND ConfirmedInvoiceNumber IS NOT NULL;

WITH DOAJobsFiltered AS (
    -- Start with most restrictive filter and pre-join with JobAssignment for early filtering
    SELECT DISTINCT
        jasi.CompanyID,
        jasi.JobAssignmentID,
        jasi.JobStatusCode,
        jasi.Date_Update,
        jasi.EmployeeID
    FROM JobAssignmentStatusInfo jasi
    INNER JOIN JobAssignment ja
        ON jasi.CompanyID = ja.CompanyID
        AND jasi.JobAssignmentID = ja.JobAssignmentID
        -- Apply business filters early to reduce dataset
        AND (ja.BillingCode LIKE 'ACC-%' OR ja.BillingCode LIKE 'AUD-%'
             OR ja.BillingCode IN ('SPE-NT', 'SPE-DD', 'SPE-OS', 'SPE-PGT', 'SPE-SR', 'SPE-ICR', 'SPE-MAR', 'SPE-PRC'))
        AND (ja.CustomerID NOT LIKE '%K' OR ja.CustomerID LIKE '%SK')
    WHERE jasi.JobStatusCode = 'DOA'
        AND jasi.Date_Update >= '2025-06-01'
        AND jasi.Date_Update < '2025-06-16'
),

AllStatusInfo AS (
    -- Get all status information for filtered jobs in one efficient pass
    SELECT
        jsi.CompanyID,
        jsi.JobAssignmentID,
        jsi.JobStatusCode,
        jsi.Date_Update,
        jsi.EmployeeID,
        -- Use window functions for better performance
        ROW_NUMBER() OVER (
            PARTITION BY jsi.CompanyID, jsi.JobAssignmentID
            ORDER BY jsi.Date_Update DESC
        ) AS StatusRank,
        -- Priority ranking for status determination
        CASE jsi.JobStatusCode
            WHEN 'N_LOCK_CNR' THEN 1
            WHEN 'LOCKED' THEN 2
            WHEN 'LOCKED_F' THEN 3
            WHEN 'CC' THEN 4
            ELSE 999
        END AS StatusPriority
    FROM JobAssignmentStatusInfo jsi
    INNER JOIN DOAJobsFiltered dj
        ON jsi.CompanyID = dj.CompanyID
        AND jsi.JobAssignmentID = dj.JobAssignmentID
    WHERE jsi.JobStatusCode <> ''
),

StatusAggregated AS (
    -- Single aggregation pass with window functions - much faster than subqueries
    SELECT
        dj.CompanyID,
        dj.JobAssignmentID,
        dj.JobStatusCode AS DOAStatusCode,
        dj.Date_Update AS DOADate,
        dj.EmployeeID AS DOAEmployeeID,
        -- Efficient aggregations without subqueries
        MIN(CASE WHEN asi.JobStatusCode = 'DOA' THEN asi.Date_Update END) AS JobStatus_DOA_MinDate,
        MAX(CASE WHEN asi.JobStatusCode = 'N_LOCK_CNR' THEN 1 ELSE 0 END) AS HasNLockCNR,
        MAX(CASE WHEN asi.JobStatusCode = 'LOCKED' THEN 1 ELSE 0 END) AS HasLocked,
        MAX(CASE WHEN asi.JobStatusCode = 'LOCKED_F' THEN 1 ELSE 0 END) AS HasLockedF,
        MAX(CASE WHEN asi.JobStatusCode = 'CC' THEN 1 ELSE 0 END) AS HasCC,
        MAX(CASE WHEN asi.JobStatusCode IN ('LOCKED', 'N_LOCK_CNR')
                      OR asi.JobStatusCode LIKE 'LOCKED_Z%'
                      OR asi.JobStatusCode LIKE 'LOCKED_S%' THEN 1 ELSE 0 END) AS HasAnyLocked,
        COUNT(CASE WHEN asi.JobStatusCode = 'DOA' THEN 1 END) AS DOACount,
        -- Get latest status using window function instead of subquery
        (SELECT TOP 1 JobStatusCode
         FROM AllStatusInfo asi2
         WHERE asi2.CompanyID = dj.CompanyID
           AND asi2.JobAssignmentID = dj.JobAssignmentID
           AND asi2.StatusRank = 1) AS LatestStatus,
        -- Get priority status efficiently
        (SELECT TOP 1 JobStatusCode
         FROM AllStatusInfo asi3
         WHERE asi3.CompanyID = dj.CompanyID
           AND asi3.JobAssignmentID = dj.JobAssignmentID
           AND asi3.StatusPriority < 999
         ORDER BY asi3.StatusPriority) AS PriorityStatus
    FROM DOAJobsFiltered dj
    LEFT JOIN AllStatusInfo asi
        ON dj.CompanyID = asi.CompanyID
        AND dj.JobAssignmentID = asi.JobAssignmentID
    GROUP BY dj.CompanyID, dj.JobAssignmentID, dj.JobStatusCode, dj.Date_Update, dj.EmployeeID
),

StatusCalculated AS (
    SELECT
        *,
        -- Calculate current status efficiently using priority logic
        COALESCE(PriorityStatus, LatestStatus) AS CurrentStatus,
        -- Calculate filter status
        CASE
            WHEN HasAnyLocked = 1 OR HasLockedF = 1 THEN 'LOCKED'
            WHEN HasCC = 1 THEN 'CC'
            ELSE COALESCE(PriorityStatus, LatestStatus)
        END AS FilterStatus
    FROM StatusAggregated
),

InvoiceAggregated AS (
    -- Pre-aggregate invoice data with better performance
    SELECT
        dj.CompanyID,
        dj.JobAssignmentID,
        COUNT(ih.ConfirmedInvoiceNumber) AS NumberOfInvoice,
        MAX(ih.InvoiceDate) AS LastInvoicedDate,
        SUM(id.Amount) AS TotalInvoicedAmount,
        STRING_AGG(RTRIM(ih.ConfirmedInvoiceNumber), ', ') AS InvoiceList
    FROM DOAJobsFiltered dj
    INNER JOIN InvoiceDetail id
        ON dj.CompanyID = id.CompanyID
        AND dj.JobAssignmentID = id.SourceID
        AND id.SourceType = 'J'
    INNER JOIN InvoiceHead ih
        ON id.CompanyID = ih.CompanyID
        AND id.UniqueID = ih.UniqueID
        AND ih.Status = 1
        AND ih.ConfirmedInvoiceNumber IS NOT NULL
        AND ih.ConfirmedInvoiceNumber <> ''
    GROUP BY dj.CompanyID, dj.JobAssignmentID
),

-- Pre-calculate employee names to avoid repeated function calls
EmployeeNames AS (
    SELECT DISTINCT
        sc.CompanyID,
        sc.JobAssignmentID,
        ja.Assistant,
        ja.Incharge,
        cus.InCharge,
        sc.DOAEmployeeID,
        -- Pre-calculate all employee names
        dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge, 1, 'A') AS InChargeOfCustomer,
        dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant, 1, 'A') AS JobAssistantName,
        dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge, 1, 'A') AS JobIncharge,
        dbo.GetEmployeeNickOrFormatNameByID(sc.DOAEmployeeID, 1, 'A') AS DOAUpdateBy
    FROM StatusCalculated sc
    INNER JOIN JobAssignment ja
        ON sc.CompanyID = ja.CompanyID
        AND sc.JobAssignmentID = ja.JobAssignmentID
    INNER JOIN Customer cus
        ON ja.CompanyID = cus.CompanyID
        AND ja.CustomerID = cus.CustomerID
)

-- Main ultra-optimized query with pre-calculated values
SELECT DISTINCT
    en.InChargeOfCustomer,
    RTRIM(ja.Team) AS JobTeam,
    RTRIM(ja.CustomerID) AS CustomerID,
    CASE
        WHEN RTRIM(ISNULL(cus.CompanyName_A1, '')) <> ''
        THEN RTRIM(cus.CompanyName_A1) + ' ' + ISNULL(cus.CompanyName_A2, '')
        WHEN RTRIM(ISNULL(cus.CompanyName_B1, '')) <> ''
        THEN RTRIM(cus.CompanyName_B1) + ' ' + ISNULL(cus.CompanyName_B2, '')
        ELSE RTRIM(ISNULL(cus.CompanyName_C1, '')) + ' ' + ISNULL(cus.CompanyName_C2, '')
    END AS CustomerName,
    RTRIM(ja.JobAssignmentID) AS JobAssignmentID,
    sc.CurrentStatus AS CurrentJobStatusCode,
    ISNULL(CONVERT(CHAR(10), sc.JobStatus_DOA_MinDate, 23), '') AS DOADate,
    en.DOAUpdateBy,
    ISNULL(CONVERT(CHAR(10), ja.InternalDeadLine, 23), '') AS JobInternalDeadLine,
    CASE dbo.RBCheckCorrectJobStatusOrder(ja.CompanyID, ja.JobAssignmentID)
        WHEN 1 THEN 'No' ELSE 'Yes' END AS JobStatusOrderCheck,
    sc.DOACount AS JobStatus_DOA_Count,
    en.JobAssistantName,
    en.JobIncharge,
    ISNULL(inv.NumberOfInvoice, 0) AS NumberOfInvoice,
    ISNULL(inv.InvoiceList, '') AS JobAssignmentInvoiceList,
    ISNULL(CONVERT(CHAR(10), inv.LastInvoicedDate, 23), '') AS LastInvoicedDate,
    ISNULL(inv.TotalInvoicedAmount, 0) AS TotalInvoicedAmount

FROM StatusCalculated sc
INNER JOIN JobAssignment ja
    ON sc.CompanyID = ja.CompanyID
    AND sc.JobAssignmentID = ja.JobAssignmentID
INNER JOIN Customer cus
    ON ja.CompanyID = cus.CompanyID
    AND ja.CustomerID = cus.CustomerID
INNER JOIN EmployeeNames en
    ON sc.CompanyID = en.CompanyID
    AND sc.JobAssignmentID = en.JobAssignmentID
LEFT JOIN InvoiceAggregated inv
    ON sc.CompanyID = inv.CompanyID
    AND sc.JobAssignmentID = inv.JobAssignmentID

WHERE sc.FilterStatus <> 'LOCKED'

ORDER BY
    en.InChargeOfCustomer,
    RTRIM(ja.Team),
    en.JobIncharge

-- Performance hints for optimal execution
OPTION (RECOMPILE, MAXDOP 4, OPTIMIZE FOR UNKNOWN);
