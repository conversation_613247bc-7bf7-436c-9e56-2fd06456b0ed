/**
 * File operations manager for copying, moving, and deleting audit folders
 */

import fs from 'fs-extra';
import path from 'path';
import { 
  getPathComponents,
  normalizePath 
} from '../shared/path-utils.js';
import { 
  ERROR_CODES, 
  PROGRESS_STAGES, 
  DEFAULT_CONFIG 
} from '../shared/constants.js';

export class FileOperationsManager {
  constructor(sseManager, logger) {
    this.sseManager = sseManager;
    this.logger = logger;
    this.currentOperations = new Map();
    this.config = {
      chunkSize: DEFAULT_CONFIG.CHUNK_SIZE,
      retryAttempts: DEFAULT_CONFIG.RETRY_ATTEMPTS,
      retryDelay: DEFAULT_CONFIG.RETRY_DELAY,
      copyTimeout: DEFAULT_CONFIG.COPY_TIMEOUT
    };
  }

  /**
   * Copy audit folders from source to target locations
   * @param {string[]} inputs - Array of audit folder names
   * @param {boolean} deleteOriginal - Whether to delete original after copy
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Copy operation results
   */
  async copyAuditFolders(inputs, deleteOriginal = false, sessionId = null) {
    const operationId = `copy_${Date.now()}`;
    this.currentOperations.set(operationId, { 
      inputs, 
      sessionId, 
      deleteOriginal,
      startTime: Date.now() 
    });

    try {
      this.logger.info(`Starting copy operation for ${inputs.length} inputs`, { 
        operationId, 
        inputs, 
        deleteOriginal 
      });

      // Emit start event
      if (sessionId) {
        this.sseManager.broadcast(sessionId, {
          type: 'start',
          data: { 
            operationId, 
            totalInputs: inputs.length, 
            operation: 'copy_folders',
            deleteOriginal 
          }
        });
      }

      // Validate all paths first
      const pathValidations = await this.validateAllPaths(inputs, sessionId);
      const validInputs = pathValidations.filter(p => p.valid);
      const invalidInputs = pathValidations.filter(p => !p.valid);

      if (invalidInputs.length > 0) {
        this.logger.warn(`${invalidInputs.length} invalid paths found`, { invalidInputs });
      }

      // Check disk space requirements
      const spaceCheck = await this.checkDiskSpace(validInputs, sessionId);
      if (!spaceCheck.sufficient) {
        throw new Error(`Insufficient disk space. Required: ${spaceCheck.required}MB, Available: ${spaceCheck.available}MB`);
      }

      // Perform copy operations
      const results = [];
      for (let i = 0; i < validInputs.length; i++) {
        const pathInfo = validInputs[i];
        
        // Emit progress
        if (sessionId) {
          this.sseManager.broadcast(sessionId, {
            type: 'progress',
            data: {
              stage: PROGRESS_STAGES.COPYING_FILES,
              current: i + 1,
              total: validInputs.length,
              currentInput: pathInfo.input
            }
          });
        }

        try {
          const copyResult = await this.copySingleFolder(pathInfo, deleteOriginal, sessionId);
          results.push(copyResult);
        } catch (error) {
          this.logger.error(`Failed to copy ${pathInfo.input}:`, error);
          results.push({
            input: pathInfo.input,
            success: false,
            error: error.message
          });
        }
      }

      // Add invalid inputs to results
      invalidInputs.forEach(pathInfo => {
        results.push({
          input: pathInfo.input,
          success: false,
          error: pathInfo.error
        });
      });

      const summary = {
        totalInputs: inputs.length,
        successCount: results.filter(r => r.success).length,
        errorCount: results.filter(r => !r.success).length,
        totalCopiedSizeMB: results
          .filter(r => r.success)
          .reduce((sum, r) => sum + (r.copiedSizeMB || 0), 0),
        deletedOriginals: deleteOriginal ? results.filter(r => r.success && r.originalDeleted).length : 0
      };

      const finalResult = { results, summary, operationId };

      // Emit completion
      if (sessionId) {
        this.sseManager.broadcast(sessionId, {
          type: 'complete',
          data: finalResult
        });
      }

      this.currentOperations.delete(operationId);
      this.logger.info(`Copy operation completed`, { operationId, summary });

      return finalResult;

    } catch (error) {
      this.logger.error(`Copy operation failed:`, error);
      
      if (sessionId) {
        this.sseManager.broadcast(sessionId, {
          type: 'error',
          data: { error: error.message, operationId }
        });
      }

      this.currentOperations.delete(operationId);
      throw error;
    }
  }

  /**
   * Validate all input paths with multiple location support
   * @param {string[]} inputs - Array of audit folder names
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object[]>} - Array of validation results with found locations
   */
  async validateAllPaths(inputs, sessionId) {
    if (sessionId) {
      this.sseManager.broadcast(sessionId, {
        type: 'progress',
        data: {
          stage: PROGRESS_STAGES.INITIALIZING,
          message: 'Validating paths and finding locations...'
        }
      });
    }

    const validations = [];
    
    for (const input of inputs) {
      try {
        const pathComponents = getPathComponents(input);
        
        if (!pathComponents.valid) {
          validations.push({
            input,
            valid: false,
            error: pathComponents.error,
            foundLocations: []
          });
          continue;
        }

        // Find all possible locations
        const possibleLocations = await this.findAllPossibleLocationsForCopy(input, pathComponents);
        const foundLocations = [];

        // Check each possible location
        for (const location of possibleLocations) {
          try {
            const sourceExists = await fs.pathExists(location.sourcePath);
            if (sourceExists) {
              const sourceStats = await fs.stat(location.sourcePath);
              if (sourceStats.isDirectory()) {
                foundLocations.push({
                  ...location,
                  sourceStats,
                  lastModified: sourceStats.mtime.toISOString(),
                  size: sourceStats.size
                });
              }
            }
          } catch (error) {
            this.logger.warn(`Error checking location ${location.sourcePath}:`, error.message);
          }
        }

        if (foundLocations.length === 0) {
          validations.push({
            input,
            valid: false,
            error: `No valid source paths found for: ${input}`,
            possibleLocations,
            foundLocations: []
          });
          continue;
        }

        validations.push({
          input,
          valid: true,
          ...pathComponents,
          possibleLocations,
          foundLocations,
          primaryLocation: foundLocations.find(loc => loc.type === 'primary') || foundLocations[0]
        });

      } catch (error) {
        validations.push({
          input,
          valid: false,
          error: error.message,
          foundLocations: []
        });
      }
    }

    return validations;
  }

  /**
   * Find all possible locations for copy operations
   * @param {string} input - Audit folder name
   * @param {object} pathComponents - Base path components
   * @returns {Promise<Array>} - Array of possible location objects
   */
  async findAllPossibleLocationsForCopy(input, pathComponents) {
    const locations = [];
    
    // Add primary location
    locations.push({
      type: 'primary',
      sourcePath: pathComponents.sourcePath,
      targetPath: pathComponents.targetPath,
      description: 'Primary location based on naming convention',
      priority: 1
    });

    // For AUD-SA, check alternative years and direct paths
    if (pathComponents.auditType === 'AUD-SA') {
      // Check alternative years
      const baseYear = parseInt(pathComponents.year);
      const alternativeYears = [baseYear - 1, baseYear + 1, new Date().getFullYear()];
      
      for (const altYear of alternativeYears) {
        if (altYear !== baseYear) {
          try {
            const altInput = input.replace(/\d{4}/, altYear.toString());
            const altComponents = getPathComponents(altInput);
            if (altComponents.valid) {
              locations.push({
                type: 'alternative_year',
                sourcePath: altComponents.sourcePath,
                targetPath: altComponents.targetPath,
                description: `Alternative year: ${altYear}`,
                year: altYear.toString(),
                priority: 2
              });
            }
          } catch (error) {
            // Ignore invalid alternative paths
          }
        }
      }

      // Direct path without character mapping
      const directPath = pathComponents.sourcePath.replace(/\\[^\\]+\\[^\\]+\\([^\\]+)$/, '\\$1');
      locations.push({
        type: 'direct',
        sourcePath: directPath,
        targetPath: directPath.replace('Year', 'Lock'),
        description: 'Direct path without character mapping',
        priority: 3
      });
    }

    // For AUD-SAL, check non-Listed directory
    if (pathComponents.auditType === 'AUD-SAL') {
      const nonListedPath = pathComponents.sourcePath.replace('\\Listed\\', '\\');
      locations.push({
        type: 'non_listed',
        sourcePath: nonListedPath,
        targetPath: nonListedPath.replace('Year', 'Lock'),
        description: 'Non-Listed directory path',
        priority: 3
      });
    }

    // Check archived, completed, and backup locations
    const additionalLocations = [
      { folder: 'Archive', type: 'archived', priority: 4 },
      { folder: 'Completed', type: 'completed', priority: 4 },
      { folder: 'Backup', type: 'backup', priority: 5 },
      { folder: 'Temp', type: 'temporary', priority: 6 }
    ];

    additionalLocations.forEach(({ folder, type, priority }) => {
      const altSourcePath = pathComponents.sourcePath.replace('Year', folder);
      const altTargetPath = pathComponents.targetPath.replace('Lock', `${folder}_Lock`);
      
      locations.push({
        type,
        sourcePath: altSourcePath,
        targetPath: altTargetPath,
        description: `${folder} location`,
        priority
      });
    });

    // Sort by priority
    locations.sort((a, b) => a.priority - b.priority);

    return locations;
  }

  /**
   * Check available disk space
   * @param {object[]} validInputs - Array of valid path objects
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Disk space check result
   */
  async checkDiskSpace(validInputs, sessionId) {
    if (sessionId) {
      this.sseManager.broadcast(sessionId, {
        type: 'progress',
        data: {
          stage: PROGRESS_STAGES.INITIALIZING,
          message: 'Checking disk space requirements...'
        }
      });
    }

    let totalRequiredSize = 0;

    // Calculate total size needed
    for (const pathInfo of validInputs) {
      try {
        const size = await this.calculateDirectorySize(pathInfo.sourcePath);
        totalRequiredSize += size;
      } catch (error) {
        this.logger.warn(`Failed to calculate size for ${pathInfo.sourcePath}:`, error.message);
      }
    }

    // Get available space on target drive (assuming all targets are on same drive)
    let availableSpace = 0;
    if (validInputs.length > 0) {
      try {
        const targetPath = path.parse(validInputs[0].targetPath).root;
        const stats = await fs.stat(targetPath);
        // Note: fs.stat doesn't provide disk space info, would need a different approach
        // For now, assume sufficient space (in production, use statvfs or similar)
        availableSpace = totalRequiredSize * 2; // Assume double the required space is available
      } catch (error) {
        this.logger.warn('Could not determine available disk space, proceeding with copy');
        availableSpace = totalRequiredSize * 2;
      }
    }

    const requiredMB = Math.round(totalRequiredSize / (1024 * 1024) * 100) / 100;
    const availableMB = Math.round(availableSpace / (1024 * 1024) * 100) / 100;

    return {
      sufficient: availableSpace >= totalRequiredSize,
      required: requiredMB,
      available: availableMB,
      totalRequired: totalRequiredSize
    };
  }

  /**
   * Calculate directory size recursively
   * @param {string} dirPath - Directory path to calculate
   * @returns {Promise<number>} - Total size in bytes
   */
  async calculateDirectorySize(dirPath) {
    let totalSize = 0;

    const processDirectory = async (currentPath) => {
      try {
        const items = await fs.readdir(currentPath);
        
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          const stats = await fs.stat(itemPath);
          
          if (stats.isFile()) {
            totalSize += stats.size;
          } else if (stats.isDirectory()) {
            await processDirectory(itemPath);
          }
        }
      } catch (error) {
        this.logger.warn(`Error calculating size for ${currentPath}:`, error.message);
      }
    };

    await processDirectory(dirPath);
    return totalSize;
  }

  /**
   * Copy a single folder with progress tracking
   * @param {object} pathInfo - Path information object
   * @param {boolean} deleteOriginal - Whether to delete original
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Copy result
   */
  async copySingleFolder(pathInfo, deleteOriginal, sessionId) {
    const startTime = Date.now();
    let copiedFiles = 0;
    let copiedSize = 0;
    let totalFiles = 0;

    try {
      // Count total files first
      totalFiles = await this.countFiles(pathInfo.sourcePath);

      // Ensure target directory exists
      await fs.ensureDir(path.dirname(pathInfo.targetPath));

      // Copy with progress tracking
      await this.copyWithProgress(
        pathInfo.sourcePath,
        pathInfo.targetPath,
        (filesCopied, bytesCopied) => {
          copiedFiles = filesCopied;
          copiedSize = bytesCopied;

          if (sessionId) {
            this.sseManager.broadcast(sessionId, {
              type: 'progress',
              data: {
                stage: PROGRESS_STAGES.COPYING_FILES,
                currentInput: pathInfo.input,
                fileProgress: {
                  copied: copiedFiles,
                  total: totalFiles,
                  percentage: Math.round((copiedFiles / totalFiles) * 100)
                },
                sizeProgress: {
                  copiedMB: Math.round(bytesCopied / (1024 * 1024) * 100) / 100
                }
              }
            });
          }
        }
      );

      // Delete original if requested
      let originalDeleted = false;
      if (deleteOriginal) {
        try {
          await fs.remove(pathInfo.sourcePath);
          originalDeleted = true;
          this.logger.info(`Deleted original folder: ${pathInfo.sourcePath}`);
        } catch (error) {
          this.logger.error(`Failed to delete original folder ${pathInfo.sourcePath}:`, error);
        }
      }

      const duration = Date.now() - startTime;
      const copiedSizeMB = Math.round(copiedSize / (1024 * 1024) * 100) / 100;

      return {
        input: pathInfo.input,
        success: true,
        sourcePath: pathInfo.sourcePath,
        targetPath: pathInfo.targetPath,
        copiedFiles,
        totalFiles,
        copiedSizeMB,
        originalDeleted,
        durationMs: duration
      };

    } catch (error) {
      throw new Error(`Copy failed for ${pathInfo.input}: ${error.message}`);
    }
  }

  /**
   * Copy directory with progress tracking
   * @param {string} src - Source path
   * @param {string} dest - Destination path
   * @param {function} progressCallback - Progress callback function
   */
  async copyWithProgress(src, dest, progressCallback) {
    let filesCopied = 0;
    let bytesCopied = 0;

    const copyRecursive = async (srcPath, destPath) => {
      const stats = await fs.stat(srcPath);

      if (stats.isDirectory()) {
        await fs.ensureDir(destPath);
        const items = await fs.readdir(srcPath);

        for (const item of items) {
          const srcItem = path.join(srcPath, item);
          const destItem = path.join(destPath, item);
          await copyRecursive(srcItem, destItem);
        }
      } else if (stats.isFile()) {
        await fs.copy(srcPath, destPath);
        filesCopied++;
        bytesCopied += stats.size;
        
        if (progressCallback && filesCopied % 10 === 0) {
          progressCallback(filesCopied, bytesCopied);
        }
      }
    };

    await copyRecursive(src, dest);
    
    // Final progress update
    if (progressCallback) {
      progressCallback(filesCopied, bytesCopied);
    }
  }

  /**
   * Count total files in directory
   * @param {string} dirPath - Directory to count
   * @returns {Promise<number>} - Total file count
   */
  async countFiles(dirPath) {
    let count = 0;

    const countRecursive = async (currentPath) => {
      try {
        const items = await fs.readdir(currentPath);
        
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          const stats = await fs.stat(itemPath);
          
          if (stats.isFile()) {
            count++;
          } else if (stats.isDirectory()) {
            await countRecursive(itemPath);
          }
        }
      } catch (error) {
        this.logger.warn(`Error counting files in ${currentPath}:`, error.message);
      }
    };

    await countRecursive(dirPath);
    return count;
  }

  /**
   * Get current operation status
   * @param {string} operationId - Operation ID to check
   * @returns {object|null} - Operation status or null if not found
   */
  getOperationStatus(operationId) {
    const operation = this.currentOperations.get(operationId);
    if (!operation) return null;

    return {
      operationId,
      inputs: operation.inputs,
      sessionId: operation.sessionId,
      deleteOriginal: operation.deleteOriginal,
      startTime: operation.startTime,
      duration: Date.now() - operation.startTime
    };
  }

  /**
   * Get all current operations
   * @returns {object[]} - Array of current operations
   */
  getCurrentOperations() {
    return Array.from(this.currentOperations.entries()).map(([id, op]) => ({
      operationId: id,
      ...this.getOperationStatus(id)
    }));
  }

  /**
   * Cancel an operation
   * @param {string} operationId - Operation ID to cancel
   * @returns {boolean} - True if operation was cancelled
   */
  cancelOperation(operationId) {
    const operation = this.currentOperations.get(operationId);
    if (!operation) return false;

    // Mark operation as cancelled
    operation.cancelled = true;
    
    // Emit cancellation event
    if (operation.sessionId) {
      this.sseManager.broadcast(operation.sessionId, {
        type: 'error',
        data: { 
          error: 'Operation cancelled',
          operationId,
          cancelled: true
        }
      });
    }

    this.currentOperations.delete(operationId);
    this.logger.info(`Operation cancelled: ${operationId}`);
    
    return true;
  }
}
