/**
 * Input validation utilities for audit path analyzer
 */

import { FILE_PATTERNS, ERROR_CODES } from './constants.js';

/**
 * Validation error class
 */
export class ValidationError extends Error {
  constructor(message, code = ERROR_CODES.VALIDATION_ERROR, details = {}) {
    super(message);
    this.name = 'ValidationError';
    this.code = code;
    this.details = details;
  }
}

/**
 * Validate audit folder name format
 * @param {string} folderName - Folder name to validate
 * @throws {ValidationError} If validation fails
 */
export function validateAuditFolderName(folderName) {
  if (!folderName || typeof folderName !== 'string') {
    throw new ValidationError('Folder name must be a non-empty string', ERROR_CODES.VALIDATION_ERROR, {
      folderName,
      type: typeof folderName
    });
  }

  if (!FILE_PATTERNS.AUDIT_FOLDER.test(folderName)) {
    throw new ValidationError('Invalid audit folder name format', ERROR_CODES.VALIDATION_ERROR, {
      folderName,
      expectedPattern: 'XX###-AUD-(SA|SAL)-YYYYMMDD',
      examples: ['AW001-AUD-SA-20250331', 'ES0083JL-AUD-SAL-20241231']
    });
  }
}

/**
 * Validate array of audit folder names
 * @param {string[]} inputs - Array of folder names to validate
 * @throws {ValidationError} If validation fails
 */
export function validateInputArray(inputs) {
  if (!Array.isArray(inputs)) {
    throw new ValidationError('Inputs must be an array', ERROR_CODES.VALIDATION_ERROR, {
      inputs,
      type: typeof inputs
    });
  }

  if (inputs.length === 0) {
    throw new ValidationError('Inputs array cannot be empty', ERROR_CODES.VALIDATION_ERROR, {
      length: inputs.length
    });
  }

  // Validate each input
  const errors = [];
  inputs.forEach((input, index) => {
    try {
      validateAuditFolderName(input);
    } catch (error) {
      errors.push({
        index,
        input,
        error: error.message
      });
    }
  });

  if (errors.length > 0) {
    throw new ValidationError('One or more inputs are invalid', ERROR_CODES.VALIDATION_ERROR, {
      errors,
      validCount: inputs.length - errors.length,
      totalCount: inputs.length
    });
  }
}

/**
 * Validate operations object
 * @param {object} operations - Operations configuration to validate
 * @throws {ValidationError} If validation fails
 */
export function validateOperations(operations) {
  if (operations && typeof operations !== 'object') {
    throw new ValidationError('Operations must be an object', ERROR_CODES.VALIDATION_ERROR, {
      operations,
      type: typeof operations
    });
  }

  const validOperations = ['checkPaths', 'analyzeSizes', 'countFiles', 'findLongNames', 'copyAndMove'];
  
  if (operations) {
    for (const [key, value] of Object.entries(operations)) {
      if (!validOperations.includes(key)) {
        throw new ValidationError(`Invalid operation: ${key}`, ERROR_CODES.VALIDATION_ERROR, {
          invalidOperation: key,
          validOperations
        });
      }

      if (typeof value !== 'boolean') {
        throw new ValidationError(`Operation ${key} must be a boolean`, ERROR_CODES.VALIDATION_ERROR, {
          operation: key,
          value,
          type: typeof value
        });
      }
    }
  }
}

/**
 * Validate session ID format
 * @param {string} sessionId - Session ID to validate
 * @throws {ValidationError} If validation fails
 */
export function validateSessionId(sessionId) {
  if (sessionId && typeof sessionId !== 'string') {
    throw new ValidationError('Session ID must be a string', ERROR_CODES.VALIDATION_ERROR, {
      sessionId,
      type: typeof sessionId
    });
  }

  if (sessionId && sessionId.length < 3) {
    throw new ValidationError('Session ID must be at least 3 characters long', ERROR_CODES.VALIDATION_ERROR, {
      sessionId,
      length: sessionId.length
    });
  }

  if (sessionId && sessionId.length > 100) {
    throw new ValidationError('Session ID must be less than 100 characters', ERROR_CODES.VALIDATION_ERROR, {
      sessionId,
      length: sessionId.length
    });
  }

  // Check for invalid characters
  if (sessionId && !/^[a-zA-Z0-9_-]+$/.test(sessionId)) {
    throw new ValidationError('Session ID can only contain alphanumeric characters, underscores, and hyphens', ERROR_CODES.VALIDATION_ERROR, {
      sessionId,
      allowedPattern: '[a-zA-Z0-9_-]+'
    });
  }
}

/**
 * Validate max length parameter
 * @param {number} maxLength - Maximum length value to validate
 * @throws {ValidationError} If validation fails
 */
export function validateMaxLength(maxLength) {
  if (maxLength !== undefined && typeof maxLength !== 'number') {
    throw new ValidationError('Max length must be a number', ERROR_CODES.VALIDATION_ERROR, {
      maxLength,
      type: typeof maxLength
    });
  }

  if (maxLength !== undefined && !Number.isInteger(maxLength)) {
    throw new ValidationError('Max length must be an integer', ERROR_CODES.VALIDATION_ERROR, {
      maxLength
    });
  }

  if (maxLength !== undefined && maxLength < 1) {
    throw new ValidationError('Max length must be greater than 0', ERROR_CODES.VALIDATION_ERROR, {
      maxLength
    });
  }

  if (maxLength !== undefined && maxLength > 1000) {
    throw new ValidationError('Max length must be less than 1000', ERROR_CODES.VALIDATION_ERROR, {
      maxLength
    });
  }
}

/**
 * Validate delete original parameter
 * @param {boolean} deleteOriginal - Delete original flag to validate
 * @throws {ValidationError} If validation fails
 */
export function validateDeleteOriginal(deleteOriginal) {
  if (deleteOriginal !== undefined && typeof deleteOriginal !== 'boolean') {
    throw new ValidationError('Delete original must be a boolean', ERROR_CODES.VALIDATION_ERROR, {
      deleteOriginal,
      type: typeof deleteOriginal
    });
  }
}

/**
 * Validate analyze audit paths parameters
 * @param {object} params - Parameters to validate
 * @throws {ValidationError} If validation fails
 */
export function validateAnalyzeAuditPathsParams(params) {
  const { inputs, operations, sessionId } = params;

  validateInputArray(inputs);
  validateOperations(operations);
  validateSessionId(sessionId);
}

/**
 * Validate copy audit folders parameters
 * @param {object} params - Parameters to validate
 * @throws {ValidationError} If validation fails
 */
export function validateCopyAuditFoldersParams(params) {
  const { inputs, deleteOriginal, sessionId } = params;

  validateInputArray(inputs);
  validateDeleteOriginal(deleteOriginal);
  validateSessionId(sessionId);
}

/**
 * Validate generate long names report parameters
 * @param {object} params - Parameters to validate
 * @throws {ValidationError} If validation fails
 */
export function validateGenerateLongNamesReportParams(params) {
  const { inputs, maxLength, sessionId } = params;

  validateInputArray(inputs);
  validateMaxLength(maxLength);
  validateSessionId(sessionId);
}

/**
 * Sanitize string input to prevent injection attacks
 * @param {string} input - Input string to sanitize
 * @returns {string} - Sanitized string
 */
export function sanitizeString(input) {
  if (typeof input !== 'string') {
    return String(input);
  }

  // Remove potential script tags and other dangerous content
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();
}

/**
 * Sanitize audit folder name
 * @param {string} folderName - Folder name to sanitize
 * @returns {string} - Sanitized folder name
 */
export function sanitizeAuditFolderName(folderName) {
  const sanitized = sanitizeString(folderName);
  
  // Only allow valid audit folder name characters
  return sanitized.replace(/[^A-Za-z0-9\-]/g, '');
}

/**
 * Validate and sanitize all inputs in a batch
 * @param {string[]} inputs - Array of inputs to validate and sanitize
 * @returns {string[]} - Array of validated and sanitized inputs
 * @throws {ValidationError} If validation fails
 */
export function validateAndSanitizeInputs(inputs) {
  validateInputArray(inputs);
  
  return inputs.map(input => {
    const sanitized = sanitizeAuditFolderName(input);
    validateAuditFolderName(sanitized);
    return sanitized;
  });
}

/**
 * Create validation result object
 * @param {boolean} isValid - Whether validation passed
 * @param {string} message - Validation message
 * @param {object} details - Additional details
 * @returns {object} - Validation result
 */
export function createValidationResult(isValid, message = '', details = {}) {
  return {
    isValid,
    message,
    details,
    timestamp: new Date().toISOString()
  };
}

/**
 * Safe validation wrapper that returns result instead of throwing
 * @param {function} validationFn - Validation function to execute
 * @param {...any} args - Arguments to pass to validation function
 * @returns {object} - Validation result
 */
export function safeValidate(validationFn, ...args) {
  try {
    validationFn(...args);
    return createValidationResult(true, 'Validation passed');
  } catch (error) {
    return createValidationResult(false, error.message, {
      code: error.code,
      details: error.details
    });
  }
}
