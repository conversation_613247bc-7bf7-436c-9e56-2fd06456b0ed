/**
 * Logger utility for audit path analyzer
 */

import { LOG_LEVELS } from './constants.js';

export class Logger {
  constructor(level = LOG_LEVELS.INFO, enableConsole = true, enableFile = false) {
    this.level = level;
    this.enableConsole = enableConsole;
    this.enableFile = enableFile;
    this.logFile = null;
    
    // Log level hierarchy
    this.levels = {
      [LOG_LEVELS.DEBUG]: 0,
      [LOG_LEVELS.INFO]: 1,
      [LOG_LEVELS.WARN]: 2,
      [LOG_LEVELS.ERROR]: 3
    };
  }

  /**
   * Check if message should be logged based on current level
   * @param {string} messageLevel - Level of the message
   * @returns {boolean} - True if should log
   */
  shouldLog(messageLevel) {
    return this.levels[messageLevel] >= this.levels[this.level];
  }

  /**
   * Format log message
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {object} meta - Additional metadata
   * @returns {string} - Formatted message
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}`;
  }

  /**
   * Log a message
   * @param {string} level - Log level
   * @param {string} message - Message to log
   * @param {object} meta - Additional metadata
   */
  log(level, message, meta = {}) {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, meta);

    if (this.enableConsole) {
      const consoleMethod = this.getConsoleMethod(level);
      consoleMethod(formattedMessage);
    }

    if (this.enableFile && this.logFile) {
      // File logging would be implemented here
      // For now, just console output
    }
  }

  /**
   * Get appropriate console method for log level
   * @param {string} level - Log level
   * @returns {function} - Console method
   */
  getConsoleMethod(level) {
    switch (level) {
      case LOG_LEVELS.DEBUG:
        return console.debug;
      case LOG_LEVELS.INFO:
        return console.info;
      case LOG_LEVELS.WARN:
        return console.warn;
      case LOG_LEVELS.ERROR:
        return console.error;
      default:
        return console.log;
    }
  }

  /**
   * Log debug message
   * @param {string} message - Message to log
   * @param {object} meta - Additional metadata
   */
  debug(message, meta = {}) {
    this.log(LOG_LEVELS.DEBUG, message, meta);
  }

  /**
   * Log info message
   * @param {string} message - Message to log
   * @param {object} meta - Additional metadata
   */
  info(message, meta = {}) {
    this.log(LOG_LEVELS.INFO, message, meta);
  }

  /**
   * Log warning message
   * @param {string} message - Message to log
   * @param {object} meta - Additional metadata
   */
  warn(message, meta = {}) {
    this.log(LOG_LEVELS.WARN, message, meta);
  }

  /**
   * Log error message
   * @param {string} message - Message to log
   * @param {object} meta - Additional metadata
   */
  error(message, meta = {}) {
    this.log(LOG_LEVELS.ERROR, message, meta);
  }

  /**
   * Set log level
   * @param {string} level - New log level
   */
  setLevel(level) {
    if (this.levels.hasOwnProperty(level)) {
      this.level = level;
      this.info(`Log level set to: ${level}`);
    } else {
      this.warn(`Invalid log level: ${level}`);
    }
  }

  /**
   * Enable or disable console logging
   * @param {boolean} enabled - Whether to enable console logging
   */
  setConsoleLogging(enabled) {
    this.enableConsole = enabled;
    this.info(`Console logging ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Create a child logger with additional context
   * @param {object} context - Additional context to include in all logs
   * @returns {Logger} - Child logger instance
   */
  child(context = {}) {
    const childLogger = new Logger(this.level, this.enableConsole, this.enableFile);
    
    // Override log method to include context
    const originalLog = childLogger.log.bind(childLogger);
    childLogger.log = (level, message, meta = {}) => {
      originalLog(level, message, { ...context, ...meta });
    };

    return childLogger;
  }
}

// Create default logger instance
export const logger = new Logger();
