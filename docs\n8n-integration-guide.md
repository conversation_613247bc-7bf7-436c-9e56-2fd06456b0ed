# n8n Integration Guide - Audit Path Analyzer MCP Server

## Overview

This guide provides comprehensive instructions for integrating the Audit Path Analyzer MCP Server with n8n workflows. The integration enables powerful audit file management capabilities within n8n's visual workflow environment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [n8n Server Registration](#n8n-server-registration)
5. [Workflow Examples](#workflow-examples)
6. [Advanced Configuration](#advanced-configuration)
7. [Troubleshooting](#troubleshooting)
8. [Performance Optimization](#performance-optimization)

## Prerequisites

### System Requirements
- **Node.js**: Version 18.0.0 or higher
- **n8n**: Version 1.0.0 or higher
- **Memory**: Minimum 512MB available RAM
- **Disk Space**: 100MB for application + storage for reports
- **Network**: Access to audit file directories (H:\FILES\2_AUDIT DEPT\)

### Permissions Required
- **File System**: Read access to audit directories, write access to output directories
- **Network**: Inbound connections on port 3001 (configurable)
- **Process**: Ability to spawn Node.js processes for MCP server

## Installation

### Step 1: Project Setup
```bash
# Clone or extract the audit-path-analyzer project
cd audit-path-analyzer

# Install dependencies
npm install

# Verify installation
npm run n8n:validate
```

### Step 2: Directory Structure
```bash
# Create required output directories
mkdir -p output/reports
mkdir -p output/long-names
mkdir -p logs

# Set appropriate permissions (Windows)
icacls output /grant Users:F /T

# Set appropriate permissions (Linux/Mac)
chmod -R 755 output
```

### Step 3: Configuration Files
Ensure the following configuration files are properly set up:

1. **config/n8n-mcp-config.json** - n8n-specific MCP configuration
2. **config/path-mappings.json** - Audit path mappings
3. **n8n-manifest.json** - n8n server manifest

## Configuration

### n8n MCP Configuration

The `config/n8n-mcp-config.json` file contains n8n-specific settings:

```json
{
  "name": "audit-path-analyzer",
  "version": "1.0.0",
  "transport": {
    "type": "stdio",
    "port": 3001
  },
  "environment": {
    "n8n": {
      "logLevel": "info",
      "sseHeartbeat": 15000,
      "maxConcurrentOperations": 8,
      "enableMetrics": true,
      "enableHealthChecks": true
    }
  }
}
```

### Environment Variables

Set the following environment variables for optimal n8n integration:

```bash
# Production environment
export NODE_ENV=production
export N8N_CONTEXT=true
export LOG_LEVEL=info

# Development environment
export NODE_ENV=development
export N8N_CONTEXT=true
export LOG_LEVEL=debug
```

### Path Mappings

Configure audit path mappings in `config/path-mappings.json`:

```json
{
  "basePaths": {
    "source": {
      "AUD-SA": "H:\\FILES\\2_AUDIT DEPT\\Year",
      "AUD-SAL": "H:\\FILES\\2_AUDIT DEPT\\Year"
    },
    "target": {
      "AUD-SA": "H:\\FILES\\2_AUDIT DEPT\\Lock",
      "AUD-SAL": "H:\\FILES\\2_AUDIT DEPT\\Lock"
    }
  },
  "characterMappings": {
    "A": "AC", "E": "E", "F": "FC", "G": "GC",
    "Y": "IY", "J": "JC", "M": "MC", "P": "PW",
    "Q": "Q", "R": "RL", "S": "SH", "T": "TO", "Z": "Z"
  }
}
```

## n8n Server Registration

### Method 1: Manual Registration

1. **Copy the manifest file** to your n8n configuration directory:
```bash
cp n8n-manifest.json /path/to/n8n/config/mcp-servers/
```

2. **Update n8n configuration** (settings.json):
```json
{
  "mcp": {
    "servers": {
      "audit-path-analyzer": {
        "command": "node",
        "args": ["src/n8n/n8n-server.js"],
        "cwd": "/path/to/audit-path-analyzer",
        "env": {
          "NODE_ENV": "production",
          "N8N_CONTEXT": "true",
          "LOG_LEVEL": "info"
        }
      }
    }
  }
}
```

### Method 2: Automatic Registration

1. **Start the n8n MCP server**:
```bash
npm run n8n:start
```

2. **Register with n8n** via API:
```bash
curl -X POST http://localhost:5678/api/mcp/servers \
  -H "Content-Type: application/json" \
  -d @n8n-manifest.json
```

### Method 3: Docker Integration

Create a Docker Compose configuration:

```yaml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=password
    volumes:
      - ./n8n-data:/home/<USER>/.n8n
      - ./audit-path-analyzer:/opt/audit-path-analyzer
    
  audit-path-analyzer:
    build: .
    command: npm run n8n:production
    environment:
      - NODE_ENV=production
      - N8N_CONTEXT=true
    volumes:
      - ./audit-files:/audit-files
      - ./output:/opt/audit-path-analyzer/output
```

## Workflow Examples

### Example 1: Basic Audit Analysis

```json
{
  "name": "Basic Audit Analysis",
  "nodes": [
    {
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "position": [100, 100]
    },
    {
      "name": "Set Audit Folders",
      "type": "n8n-nodes-base.set",
      "position": [300, 100],
      "parameters": {
        "values": {
          "string": [
            {
              "name": "auditFolders",
              "value": "AW001-AUD-***********,AY002-AUD-***********"
            }
          ]
        }
      }
    },
    {
      "name": "Analyze Audit Paths",
      "type": "auditPathAnalyzer.analyzeAuditPaths",
      "position": [500, 100],
      "parameters": {
        "inputs": "={{ $json.auditFolders.split(',') }}",
        "operations": {
          "checkPaths": true,
          "analyzeSizes": true,
          "countFiles": true,
          "findLongNames": true
        }
      }
    },
    {
      "name": "Process Results",
      "type": "n8n-nodes-base.function",
      "position": [700, 100],
      "parameters": {
        "functionCode": "// Process analysis results\nconst results = JSON.parse($input.first().json.content[0].text);\n\nreturn results.data.results.map(result => ({\n  auditFolder: result.input,\n  found: result.locationsFound > 0,\n  locations: result.locationsFound,\n  sizeMB: result.totalSizeMB,\n  files: result.totalFiles,\n  hasLongNames: result.hasLongNames\n}));"
      }
    }
  ],
  "connections": {
    "Manual Trigger": {
      "main": [[{"node": "Set Audit Folders", "type": "main", "index": 0}]]
    },
    "Set Audit Folders": {
      "main": [[{"node": "Analyze Audit Paths", "type": "main", "index": 0}]]
    },
    "Analyze Audit Paths": {
      "main": [[{"node": "Process Results", "type": "main", "index": 0}]]
    }
  }
}
```

### Example 2: Automated Compliance Report

```json
{
  "name": "Automated Compliance Report",
  "nodes": [
    {
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "position": [100, 100],
      "parameters": {
        "rule": {
          "interval": [{"field": "hours", "value": 24}]
        }
      }
    },
    {
      "name": "Get Audit List",
      "type": "n8n-nodes-base.httpRequest",
      "position": [300, 100],
      "parameters": {
        "url": "http://audit-system/api/folders",
        "method": "GET"
      }
    },
    {
      "name": "Generate Long Names Report",
      "type": "auditPathAnalyzer.generateLongNamesReport",
      "position": [500, 100],
      "parameters": {
        "inputs": "={{ $json.auditFolders }}",
        "maxLength": 250,
        "reportFormat": "csv"
      }
    },
    {
      "name": "Email Report",
      "type": "n8n-nodes-base.emailSend",
      "position": [700, 100],
      "parameters": {
        "toEmail": "<EMAIL>",
        "subject": "Daily Compliance Report - {{ new Date().toDateString() }}",
        "text": "Please find attached the daily audit compliance report.",
        "attachments": "={{ $json.reportFiles }}"
      }
    }
  ]
}
```

### Example 3: Bulk Copy Operation with Error Handling

```json
{
  "name": "Bulk Copy with Error Handling",
  "nodes": [
    {
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "position": [100, 100],
      "parameters": {
        "path": "audit-copy",
        "httpMethod": "POST"
      }
    },
    {
      "name": "Validate Input",
      "type": "n8n-nodes-base.if",
      "position": [300, 100],
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.auditFolders }}",
              "operation": "isNotEmpty"
            }
          ]
        }
      }
    },
    {
      "name": "Copy Audit Folders",
      "type": "auditPathAnalyzer.copyAuditFolders",
      "position": [500, 100],
      "parameters": {
        "inputs": "={{ $json.auditFolders }}",
        "deleteOriginal": false,
        "copyOptions": {
          "verifyIntegrity": true,
          "preserveTimestamps": true,
          "createBackup": true
        }
      }
    },
    {
      "name": "Handle Success",
      "type": "n8n-nodes-base.function",
      "position": [700, 50],
      "parameters": {
        "functionCode": "return [{ json: { status: 'success', message: 'All folders copied successfully' } }];"
      }
    },
    {
      "name": "Handle Error",
      "type": "n8n-nodes-base.function",
      "position": [700, 150],
      "parameters": {
        "functionCode": "return [{ json: { status: 'error', message: $input.first().json.error.message } }];"
      }
    }
  ]
}
```

## Advanced Configuration

### Custom Error Handling

Configure custom error handling for your workflows:

```javascript
// Custom error handler function
function handleAuditError(error) {
  const errorData = JSON.parse(error.json.content[0].text);
  
  if (errorData.error.retryable) {
    // Implement retry logic
    return { retry: true, delay: 5000 };
  } else {
    // Log error and notify
    console.error('Non-retryable error:', errorData.error.message);
    return { retry: false, notify: true };
  }
}
```

### Performance Tuning

Optimize performance for large-scale operations:

```json
{
  "n8n": {
    "maxConcurrentOperations": 8,
    "enableBatching": true,
    "batchSize": 10,
    "timeoutMs": 300000,
    "retryAttempts": 3,
    "retryDelay": 5000
  }
}
```

### SSE Progress Monitoring

Set up real-time progress monitoring:

```javascript
// SSE progress handler
const eventSource = new EventSource(`http://localhost:3001/events/${sessionId}`);

eventSource.onmessage = function(event) {
  const progress = JSON.parse(event.data);
  
  if (progress.type === 'n8n_workflow_progress') {
    updateWorkflowProgress(progress.stage, progress.progress);
  }
};
```

## Troubleshooting

### Common Issues

#### 1. Server Connection Failed
```bash
# Check server status
npm run n8n:start

# Verify health endpoint
curl http://localhost:3001/health

# Check logs
tail -f logs/n8n-server.log
```

#### 2. Permission Denied Errors
```bash
# Windows - Grant permissions
icacls "H:\FILES\2_AUDIT DEPT" /grant Users:R /T

# Linux/Mac - Check permissions
ls -la /path/to/audit/files
```

#### 3. Configuration Issues
```bash
# Validate configuration
npm run n8n:validate

# Check configuration files
node -e "console.log(JSON.parse(require('fs').readFileSync('config/n8n-mcp-config.json')))"
```

#### 4. Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Monitor memory usage
node --inspect src/n8n/n8n-server.js
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Start in debug mode
DEBUG=audit-path-analyzer:* npm run n8n:dev

# View debug logs
tail -f logs/debug.log
```

### Error Codes Reference

| Error Code | Description | Solution |
|------------|-------------|----------|
| INPUT_VALIDATION_ERROR | Invalid input format | Check audit folder name format |
| FILE_NOT_FOUND | Audit folder not found | Verify folder exists and paths are correct |
| PERMISSION_DENIED | Access denied | Check file/directory permissions |
| WORKFLOW_TIMEOUT | Operation timed out | Increase timeout or reduce batch size |
| NETWORK_ERROR | Network connectivity issue | Check network connection and firewall |

## Performance Optimization

### Batch Processing

For large numbers of audit folders:

```json
{
  "name": "Batch Processing",
  "parameters": {
    "inputs": "={{ $json.allFolders.slice(0, 10) }}",
    "operations": {
      "enableBatching": true,
      "batchSize": 5,
      "enableParallel": false
    }
  }
}
```

### Caching Strategy

Implement caching for repeated operations:

```javascript
// Cache results for 1 hour
const cacheKey = `audit_analysis_${JSON.stringify(inputs)}`;
const cachedResult = cache.get(cacheKey);

if (cachedResult) {
  return cachedResult;
}

// Perform analysis and cache result
const result = await analyzeAuditPaths(inputs);
cache.set(cacheKey, result, 3600); // 1 hour TTL
```

### Resource Monitoring

Monitor system resources during operations:

```bash
# Monitor CPU and memory
htop

# Monitor disk I/O
iotop

# Monitor network
netstat -an | grep 3001
```

## Best Practices

### Workflow Design
1. **Use error handling nodes** for all audit operations
2. **Implement retry logic** for transient failures
3. **Add progress monitoring** for long-running operations
4. **Use batch processing** for large datasets
5. **Include logging** for audit trail compliance

### Security
1. **Validate all inputs** before processing
2. **Use least privilege** file system permissions
3. **Encrypt sensitive data** in transit
4. **Monitor access logs** for unauthorized access
5. **Regular security updates** for dependencies

### Maintenance
1. **Regular backups** of configuration files
2. **Log rotation** to manage disk space
3. **Performance monitoring** and optimization
4. **Regular testing** of workflows
5. **Documentation updates** for changes

## Support and Resources

### Documentation
- [Operation Guide](operation-guide.md)
- [API Reference](api-reference.md)
- [Test Plan](test-plan.md)

### Examples
- [Workflow Examples](../examples/n8n-workflows/)
- [Configuration Examples](../examples/configurations/)

### Support Channels
- Email: <EMAIL>
- Documentation: This guide and related docs
- Issues: GitHub issues or internal ticketing system

### Version Compatibility

| Component | Version | Compatibility |
|-----------|---------|---------------|
| Node.js | 18.0.0+ | Required |
| n8n | 1.0.0+ | Recommended |
| Audit Path Analyzer | 1.0.0 | Current |

This integration guide provides comprehensive instructions for successfully deploying and using the Audit Path Analyzer MCP Server with n8n workflows.
