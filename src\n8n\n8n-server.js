/**
 * n8n MCP Server Wrapper for Audit Path Analyzer
 * This file provides n8n-specific integration for the audit path analyzer
 */

import { readFileSync } from 'fs';
import { AuditPathAnalyzerServer } from '../server/mcp-server.js';
import { Logger } from '../shared/logger.js';
import { N8nToolHandlers } from './n8n-tool-handlers.js';
import { N8nFormatters } from './n8n-formatters.js';
import { N8nErrorHandlers } from './n8n-error-handlers.js';

export class N8nAuditPathAnalyzerServer {
  constructor() {
    this.config = this.loadConfig();
    this.logger = new Logger(this.config.environment.n8n.logLevel);
    this.server = null;
    this.toolHandlers = new N8nToolHandlers(this.logger);
    this.formatters = new N8nFormatters();
    this.errorHandlers = new N8nErrorHandlers(this.logger);
    
    // n8n-specific tracking
    this.n8nMetrics = {
      workflowExecutions: new Map(),
      performanceData: [],
      errorCounts: new Map()
    };
    
    this.setupProcessHandlers();
  }

  /**
   * Load n8n-specific configuration
   */
  loadConfig() {
    try {
      const n8nConfig = JSON.parse(readFileSync('./config/n8n-mcp-config.json', 'utf8'));
      const pathMappings = JSON.parse(readFileSync('./config/path-mappings.json', 'utf8'));
      
      return {
        ...n8nConfig,
        pathMappings,
        environment: n8nConfig.environment.n8n
      };
    } catch (error) {
      console.error('Failed to load n8n configuration:', error);
      throw new Error('n8n configuration not found or invalid');
    }
  }

  /**
   * Setup process handlers for n8n environment
   */
  setupProcessHandlers() {
    // Handle graceful shutdown for n8n
    process.on('SIGINT', () => this.shutdown('SIGINT'));
    process.on('SIGTERM', () => this.shutdown('SIGTERM'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception in n8n server:', error);
      this.errorHandlers.handleCriticalError(error);
    });
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled promise rejection in n8n server:', { reason, promise });
      this.errorHandlers.handlePromiseRejection(reason, promise);
    });
  }

  /**
   * Initialize and start the n8n MCP server
   */
  async start() {
    try {
      this.logger.info('Starting n8n Audit Path Analyzer MCP Server...');
      
      // Create the base server with n8n-specific config
      this.server = new AuditPathAnalyzerServer({
        ...this.config.environment,
        n8nMode: true,
        toolHandlers: this.toolHandlers,
        formatters: this.formatters,
        errorHandlers: this.errorHandlers
      });

      // Enhance server with n8n-specific functionality
      this.enhanceServerForN8n();
      
      // Start the server
      await this.server.start(this.config.transport?.port || 3001);
      
      this.logger.info('n8n MCP Server started successfully', {
        config: this.config.name,
        version: this.config.version,
        capabilities: this.config.capabilities
      });

      // Send startup notification to n8n (if running in n8n context)
      this.notifyN8nStartup();
      
    } catch (error) {
      this.logger.error('Failed to start n8n MCP server:', error);
      await this.errorHandlers.handleStartupError(error);
      throw error;
    }
  }

  /**
   * Enhance the base server with n8n-specific functionality
   */
  enhanceServerForN8n() {
    // Override tool handlers with n8n-specific versions
    const originalHandleAnalyzeAuditPaths = this.server.handleAnalyzeAuditPaths.bind(this.server);
    this.server.handleAnalyzeAuditPaths = async (args) => {
      return this.handleN8nToolCall('analyze_audit_paths', args, originalHandleAnalyzeAuditPaths);
    };

    const originalHandleCopyAuditFolders = this.server.handleCopyAuditFolders.bind(this.server);
    this.server.handleCopyAuditFolders = async (args) => {
      return this.handleN8nToolCall('copy_audit_folders', args, originalHandleCopyAuditFolders);
    };

    const originalHandleGenerateLongNamesReport = this.server.handleGenerateLongNamesReport.bind(this.server);
    this.server.handleGenerateLongNamesReport = async (args) => {
      return this.handleN8nToolCall('generate_long_names_report', args, originalHandleGenerateLongNamesReport);
    };

    const originalHandleGetOperationStatus = this.server.handleGetOperationStatus.bind(this.server);
    this.server.handleGetOperationStatus = async (args) => {
      return this.handleN8nToolCall('get_operation_status', args, originalHandleGetOperationStatus);
    };
  }

  /**
   * Handle n8n-specific tool calls with enhanced error handling and formatting
   */
  async handleN8nToolCall(toolName, args, originalHandler) {
    const startTime = Date.now();
    const executionId = this.generateExecutionId();
    
    try {
      // Extract n8n context if provided
      const n8nContext = args.n8nContext || {};
      
      // Track execution
      this.trackExecution(executionId, toolName, n8nContext, args);
      
      // Validate n8n-specific arguments
      this.toolHandlers.validateN8nArgs(toolName, args);
      
      // Add n8n-specific enhancements to args
      const enhancedArgs = this.toolHandlers.enhanceArgsForN8n(args, n8nContext);
      
      // Execute the original tool
      const result = await originalHandler(enhancedArgs);
      
      // Format result for n8n
      const formattedResult = this.formatters.formatResultForN8n(toolName, result, {
        executionId,
        executionTime: Date.now() - startTime,
        n8nContext
      });
      
      // Track successful execution
      this.trackExecutionCompletion(executionId, true);
      
      return formattedResult;
      
    } catch (error) {
      // Track failed execution
      this.trackExecutionCompletion(executionId, false, error);
      
      // Handle error with n8n-specific formatting
      return this.errorHandlers.handleN8nToolError(toolName, error, {
        executionId,
        args,
        executionTime: Date.now() - startTime
      });
    }
  }

  /**
   * Generate unique execution ID for tracking
   */
  generateExecutionId() {
    return `n8n_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Track execution start
   */
  trackExecution(executionId, toolName, n8nContext, args) {
    this.n8nMetrics.workflowExecutions.set(executionId, {
      toolName,
      startTime: Date.now(),
      n8nContext,
      inputSize: JSON.stringify(args).length,
      status: 'running'
    });
  }

  /**
   * Track execution completion
   */
  trackExecutionCompletion(executionId, success, error = null) {
    const execution = this.n8nMetrics.workflowExecutions.get(executionId);
    if (execution) {
      execution.status = success ? 'completed' : 'failed';
      execution.endTime = Date.now();
      execution.duration = execution.endTime - execution.startTime;
      execution.error = error?.message;
      
      // Move to performance data
      this.n8nMetrics.performanceData.push(execution);
      this.n8nMetrics.workflowExecutions.delete(executionId);
      
      // Update error counts
      if (!success) {
        const errorType = error?.constructor?.name || 'UnknownError';
        this.n8nMetrics.errorCounts.set(errorType, 
          (this.n8nMetrics.errorCounts.get(errorType) || 0) + 1);
      }
      
      // Cleanup old performance data (keep last 1000 entries)
      if (this.n8nMetrics.performanceData.length > 1000) {
        this.n8nMetrics.performanceData = this.n8nMetrics.performanceData.slice(-1000);
      }
    }
  }

  /**
   * Get n8n-specific metrics
   */
  getN8nMetrics() {
    return {
      activeExecutions: this.n8nMetrics.workflowExecutions.size,
      recentExecutions: this.n8nMetrics.performanceData.slice(-10),
      errorCounts: Object.fromEntries(this.n8nMetrics.errorCounts),
      averageExecutionTime: this.calculateAverageExecutionTime(),
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  /**
   * Calculate average execution time
   */
  calculateAverageExecutionTime() {
    if (this.n8nMetrics.performanceData.length === 0) return 0;
    
    const total = this.n8nMetrics.performanceData.reduce((sum, exec) => 
      sum + (exec.duration || 0), 0);
    return Math.round(total / this.n8nMetrics.performanceData.length);
  }

  /**
   * Notify n8n of server startup (if running in n8n context)
   */
  notifyN8nStartup() {
    // Check if running in n8n environment
    if (process.env.N8N_CONTEXT) {
      try {
        // Send startup notification through stdout (n8n will capture this)
        console.log(JSON.stringify({
          type: 'n8n_mcp_startup',
          server: this.config.name,
          version: this.config.version,
          capabilities: this.config.capabilities,
          tools: this.config.tools.map(t => t.name),
          timestamp: new Date().toISOString()
        }));
      } catch (error) {
        this.logger.warn('Failed to notify n8n of startup:', error);
      }
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(signal) {
    this.logger.info(`Received ${signal}, shutting down n8n MCP server gracefully...`);
    
    try {
      // Stop accepting new requests
      if (this.server) {
        await this.server.stop();
      }
      
      // Wait for active executions to complete (with timeout)
      await this.waitForActiveExecutions(5000);
      
      // Save metrics if needed
      await this.saveMetrics();
      
      this.logger.info('n8n MCP server shutdown completed');
      process.exit(0);
      
    } catch (error) {
      this.logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }

  /**
   * Wait for active executions to complete
   */
  async waitForActiveExecutions(timeout = 5000) {
    const startTime = Date.now();
    
    while (this.n8nMetrics.workflowExecutions.size > 0 && 
           (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (this.n8nMetrics.workflowExecutions.size > 0) {
      this.logger.warn(`Shutdown timeout: ${this.n8nMetrics.workflowExecutions.size} executions still active`);
    }
  }

  /**
   * Save metrics for n8n analysis
   */
  async saveMetrics() {
    try {
      const metrics = this.getN8nMetrics();
      // Could save to file or send to n8n monitoring system
      this.logger.info('Final n8n metrics:', metrics);
    } catch (error) {
      this.logger.warn('Failed to save metrics:', error);
    }
  }
}

// Main execution for n8n
if (import.meta.url === `file://${process.argv[1]}`) {
  const n8nServer = new N8nAuditPathAnalyzerServer();
  
  n8nServer.start().catch(error => {
    console.error('Failed to start n8n MCP server:', error);
    process.exit(1);
  });
}

export default N8nAuditPathAnalyzerServer;
