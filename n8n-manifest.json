{"name": "audit-path-analyzer", "version": "1.0.0", "displayName": "Audit Path Analyzer", "description": "Advanced audit file path analyzer with multiple location discovery, comprehensive analysis, and professional reporting capabilities for audit department workflows.", "icon": "audit-analyzer.svg", "category": "file-analysis", "subcategory": "audit-tools", "tags": ["audit", "file-analysis", "path-analyzer", "reporting", "compliance"], "mcp": {"protocol": "1.0.0", "transport": {"type": "stdio", "command": "node", "args": ["src/n8n/n8n-server.js"], "cwd": ".", "env": {"NODE_ENV": "production", "N8N_CONTEXT": "true"}}, "capabilities": {"tools": true, "resources": true, "prompts": false, "logging": true, "progress": true}}, "n8n": {"compatibility": {"minVersion": "1.0.0", "maxVersion": "2.0.0"}, "nodeTypes": [{"name": "auditPathAnalyzer.analyzeAuditPaths", "displayName": "Analyze Audit Paths", "description": "Analyze audit file paths with comprehensive multi-location discovery", "icon": "fa:search-plus", "group": ["analysis"], "version": 1, "properties": [{"displayName": "Audit Folder Names", "name": "inputs", "type": "string", "typeOptions": {"multipleValues": true, "multipleValueButtonText": "Add Audit Folder"}, "default": [], "placeholder": "AW001-AUD-***********", "description": "List of audit folder names to analyze", "required": true, "validation": {"type": "regex", "properties": {"regex": "^[A-Z]{2,8}\\d*[A-Z]*-AUD-(SA|SAL)-\\d{8}$", "errorMessage": "Invalid audit folder format. Use: AW001-AUD-***********"}}}, {"displayName": "Analysis Operations", "name": "operations", "type": "collection", "placeholder": "Select Operations", "default": {}, "options": [{"displayName": "Check Path Existence", "name": "checkPaths", "type": "boolean", "default": true, "description": "Verify if audit paths exist in multiple locations"}, {"displayName": "Analyze Folder Sizes", "name": "analyzeSizes", "type": "boolean", "default": true, "description": "Calculate folder sizes in MB with precision"}, {"displayName": "Count <PERSON>", "name": "countFiles", "type": "boolean", "default": true, "description": "Count total files and folders recursively"}, {"displayName": "Find Long Names/Paths", "name": "findLongNames", "type": "boolean", "default": true, "description": "Detect file/folder names or paths over 250 characters"}]}, {"displayName": "Session ID", "name": "sessionId", "type": "string", "default": "", "placeholder": "auto-generated", "description": "Optional session ID for progress tracking (auto-generated if empty)"}]}, {"name": "auditPathAnalyzer.copyAuditFolders", "displayName": "Copy Audit Folders", "description": "Copy audit folders from discovered sources to target locations", "icon": "fa:copy", "group": ["file-operations"], "version": 1, "properties": [{"displayName": "Audit Folder Names", "name": "inputs", "type": "string", "typeOptions": {"multipleValues": true, "multipleValueButtonText": "Add Audit Folder"}, "default": [], "placeholder": "AW001-AUD-***********", "description": "List of audit folder names to copy", "required": true}, {"displayName": "Delete Original", "name": "deleteOriginal", "type": "boolean", "default": false, "description": "⚠️ WARNING: Delete original folders after successful copy"}, {"displayName": "Copy Options", "name": "copyOptions", "type": "collection", "placeholder": "Advanced Options", "default": {}, "options": [{"displayName": "Verify Copy Integrity", "name": "verifyIntegrity", "type": "boolean", "default": true, "description": "Verify file integrity after copy operation"}, {"displayName": "Preserve Timestamps", "name": "preserveTimestamps", "type": "boolean", "default": true, "description": "Maintain original file timestamps"}, {"displayName": "Create Backup", "name": "createBackup", "type": "boolean", "default": false, "description": "Create backup before copy operation"}]}]}, {"name": "auditPathAnalyzer.generateLongNamesReport", "displayName": "Generate Long Names Report", "description": "Generate professional table format report of long file/folder names or paths", "icon": "fa:file-text", "group": ["reporting"], "version": 1, "properties": [{"displayName": "Audit Folder Names", "name": "inputs", "type": "string", "typeOptions": {"multipleValues": true, "multipleValueButtonText": "Add Audit Folder"}, "default": [], "required": true}, {"displayName": "Maximum Length", "name": "max<PERSON><PERSON><PERSON>", "type": "number", "default": 250, "typeOptions": {"minValue": 50, "maxValue": 1000}, "description": "Maximum allowed character length for names and paths"}, {"displayName": "Report Format", "name": "reportFormat", "type": "options", "options": [{"name": "Table Format", "value": "table"}, {"name": "CSV", "value": "csv"}, {"name": "JSON", "value": "json"}, {"name": "Excel", "value": "excel"}], "default": "table", "description": "Output format for the report"}]}, {"name": "auditPathAnalyzer.getOperationStatus", "displayName": "Get Operation Status", "description": "Check status of current or specific operations", "icon": "fa:info-circle", "group": ["monitoring"], "version": 1, "properties": [{"displayName": "Operation ID", "name": "operationId", "type": "string", "default": "", "placeholder": "Leave empty for all operations", "description": "Optional operation ID to check specific operation"}, {"displayName": "Include Details", "name": "includeDetails", "type": "boolean", "default": false, "description": "Include detailed progress and timing information"}]}]}, "documentation": {"overview": "The Audit Path Analyzer is a comprehensive MCP server designed for audit department file management. It provides advanced multi-location discovery, detailed analysis, and professional reporting capabilities.", "features": ["Multiple Location Discovery: Automatically finds audit folders across primary, archived, completed, and backup locations", "Comprehensive Analysis: Calculates folder sizes, counts files, and analyzes structure", "Long Path Detection: Identifies file/folder names or full paths exceeding 250 characters", "Professional Reporting: Generates table format reports with CSV export capability", "Real-time Progress: SSE-based progress updates for long operations", "Robust Error Handling: Comprehensive error management with workflow guidance"], "useCases": ["Audit folder analysis and reporting", "File path compliance checking", "Automated audit folder relocation", "Compliance reporting for file naming standards", "Audit trail generation and management"], "examples": [{"name": "Basic Analysis", "description": "Analyze audit folders for existence and basic metrics", "workflow": {"nodes": [{"type": "auditPathAnalyzer.analyzeAuditPaths", "parameters": {"inputs": ["AW001-AUD-***********", "AY002-AUD-***********"], "operations": {"checkPaths": true, "analyzeSizes": true, "countFiles": true, "findLongNames": false}}}]}}, {"name": "Compliance Report", "description": "Generate comprehensive compliance report with long path detection", "workflow": {"nodes": [{"type": "auditPathAnalyzer.generateLongNamesReport", "parameters": {"inputs": ["{{$json.auditFolders}}"], "maxLength": 250, "reportFormat": "csv"}}]}}]}, "requirements": {"system": {"node": ">=18.0.0", "memory": "512MB", "diskSpace": "100MB"}, "permissions": {"fileSystem": {"read": ["H:\\FILES\\2_AUDIT DEPT\\"], "write": ["H:\\FILES\\2_AUDIT DEPT\\Lock*", "./output/"]}, "network": {"outbound": false, "inbound": ["localhost:3001"]}}, "environment": {"variables": [{"name": "NODE_ENV", "description": "Runtime environment", "default": "production"}, {"name": "LOG_LEVEL", "description": "Logging level", "default": "info"}]}}, "installation": {"steps": ["Clone or extract the audit-path-analyzer project", "Run 'npm install' to install dependencies", "Configure paths in config/path-mappings.json if needed", "Create output directories: mkdir -p output/reports output/long-names", "Test with 'npm run n8n:validate' to verify configuration", "Register with n8n using this manifest file"], "verification": ["Check server starts: npm run n8n:start", "Verify health endpoint: curl http://localhost:3001/health", "Test basic analysis with sample data"]}, "support": {"documentation": "docs/n8n-integration-guide.md", "examples": "examples/n8n-workflows/", "troubleshooting": "docs/operation-guide.md#troubleshooting", "contact": "<EMAIL>"}, "changelog": {"1.0.0": {"date": "2025-01-07", "changes": ["Initial n8n integration release", "Multiple location discovery implementation", "Professional table format reporting", "Comprehensive error handling for workflows", "Real-time progress tracking via SSE"]}}}