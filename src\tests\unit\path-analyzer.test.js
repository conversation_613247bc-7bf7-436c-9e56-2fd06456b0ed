/**
 * Unit tests for path analyzer with multiple location discovery
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { PathAnalyzer } from '../../server/path-analyzer.js';

// Mock dependencies
const mockSSEManager = {
  broadcast: jest.fn()
};

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

describe('PathAnalyzer', () => {
  let pathAnalyzer;

  beforeEach(() => {
    pathAnalyzer = new PathAnalyzer(mockSSEManager, mockLogger);
    jest.clearAllMocks();
  });

  describe('findAllPossibleLocations', () => {
    test('should find multiple locations for AUD-SA input', async () => {
      const input = 'AW001-AUD-***********';
      const pathComponents = {
        sourcePath: 'H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********',
        targetPath: 'H:\\FILES\\2_AUDIT DEPT\\Lock 2025\\AC\\W\\AW001-AUD-***********',
        auditType: 'AUD-SA',
        year: '2025'
      };

      const locations = await pathAnalyzer.findAllPossibleLocations(input, pathComponents);

      expect(locations).toBeInstanceOf(Array);
      expect(locations.length).toBeGreaterThan(1);

      // Should have primary location
      const primary = locations.find(loc => loc.type === 'primary');
      expect(primary).toBeDefined();
      expect(primary.sourcePath).toBe(pathComponents.sourcePath);

      // Should have archived location
      const archived = locations.find(loc => loc.type === 'archived');
      expect(archived).toBeDefined();
      expect(archived.sourcePath).toContain('Archive');

      // Should have completed location
      const completed = locations.find(loc => loc.type === 'completed');
      expect(completed).toBeDefined();
      expect(completed.sourcePath).toContain('Completed');
    });

    test('should find multiple locations for AUD-SAL input', async () => {
      const input = 'ES0083JL-AUD-SAL-20241231';
      const pathComponents = {
        sourcePath: 'H:\\FILES\\2_AUDIT DEPT\\Year 2024\\Listed\\E\\ES0083JL',
        targetPath: 'H:\\FILES\\2_AUDIT DEPT\\Lock 2024\\Listed\\E\\ES0083JL',
        auditType: 'AUD-SAL',
        year: '2024'
      };

      const locations = await pathAnalyzer.findAllPossibleLocations(input, pathComponents);

      expect(locations).toBeInstanceOf(Array);
      expect(locations.length).toBeGreaterThan(1);

      // Should have primary location
      const primary = locations.find(loc => loc.type === 'primary');
      expect(primary).toBeDefined();

      // Should have non-listed variation
      const nonListed = locations.find(loc => loc.type === 'non_listed');
      expect(nonListed).toBeDefined();
      expect(nonListed.sourcePath).not.toContain('Listed');
    });
  });

  describe('getAllFullPaths', () => {
    test('should return empty array for non-existent directory', async () => {
      const result = await pathAnalyzer.getAllFullPaths('non-existent-path');
      expect(result).toEqual([]);
    });

    test('should handle errors gracefully', async () => {
      // Mock fs.readdir to throw an error
      const originalReaddir = require('fs-extra').readdir;
      require('fs-extra').readdir = jest.fn().mockRejectedValue(new Error('Permission denied'));

      const result = await pathAnalyzer.getAllFullPaths('/some/path');
      expect(result).toEqual([]);

      // Restore original function
      require('fs-extra').readdir = originalReaddir;
    });
  });

  describe('analyzeLocation', () => {
    test('should return non-existent location result when path does not exist', async () => {
      const location = {
        type: 'primary',
        sourcePath: 'non-existent-path',
        targetPath: 'target-path',
        description: 'Test location'
      };

      const result = await pathAnalyzer.analyzeLocation(location, { checkPaths: true }, null);

      expect(result.exists).toBe(false);
      expect(result.type).toBe('primary');
      expect(result.sourcePath).toBe('non-existent-path');
      expect(result.fullPathsList).toEqual([]);
    });

    test('should include fullPathsList in result structure', async () => {
      const location = {
        type: 'primary',
        sourcePath: 'test-path',
        targetPath: 'target-path',
        description: 'Test location'
      };

      // Mock checkPathExists to return false
      pathAnalyzer.checkPathExists = jest.fn().mockResolvedValue(false);

      const result = await pathAnalyzer.analyzeLocation(location, { checkPaths: true }, null);

      expect(result).toHaveProperty('fullPathsList');
      expect(result.fullPathsList).toEqual([]);
    });

    test('should handle all operations when path exists', async () => {
      const location = {
        type: 'primary',
        sourcePath: 'existing-path',
        targetPath: 'target-path',
        description: 'Test location'
      };

      // Mock all the required methods
      pathAnalyzer.checkPathExists = jest.fn().mockResolvedValue(true);
      pathAnalyzer.getAllFullPaths = jest.fn().mockResolvedValue(['path1', 'path2']);
      pathAnalyzer.analyzeFolderStats = jest.fn().mockResolvedValue({
        sizeInMB: 100.5,
        fileCount: 50,
        folderCount: 5
      });
      pathAnalyzer.checkLongNames = jest.fn().mockResolvedValue('long-names-report.txt');

      // Mock fs.stat and fs.access
      const mockStat = jest.fn().mockResolvedValue({
        mtime: new Date('2025-01-07T09:00:00.000Z')
      });
      const mockAccess = jest.fn().mockResolvedValue(undefined);

      require('fs-extra').stat = mockStat;
      require('fs-extra').access = mockAccess;

      const operations = {
        checkPaths: true,
        analyzeSizes: true,
        countFiles: true,
        findLongNames: true
      };

      const result = await pathAnalyzer.analyzeLocation(location, operations, 'test-session');

      expect(result.exists).toBe(true);
      expect(result.fullPathsList).toEqual(['path1', 'path2']);
      expect(result.sizeInMB).toBe(100.5);
      expect(result.fileCount).toBe(50);
      expect(result.folderCount).toBe(5);
      expect(result.longNamesFile).toBe('long-names-report.txt');
      expect(result.lastModified).toBe('2025-01-07T09:00:00.000Z');
      expect(result.permissions.readable).toBe(true);
      expect(result.permissions.writable).toBe(true);
    });
  });

  describe('analyzeSinglePath', () => {
    test('should return error for invalid input', async () => {
      await expect(pathAnalyzer.analyzeSinglePath('INVALID-NAME', {}, null))
        .rejects.toThrow();
    });

    test('should return multiple location analysis for valid input', async () => {
      // Mock the required methods
      pathAnalyzer.findAllPossibleLocations = jest.fn().mockResolvedValue([
        {
          type: 'primary',
          sourcePath: 'path1',
          targetPath: 'target1',
          description: 'Primary'
        },
        {
          type: 'archived',
          sourcePath: 'path2',
          targetPath: 'target2',
          description: 'Archived'
        }
      ]);

      pathAnalyzer.analyzeLocation = jest.fn()
        .mockResolvedValueOnce({
          type: 'primary',
          exists: true,
          sizeInMB: 100,
          fileCount: 50,
          longNamesFile: 'report1.txt'
        })
        .mockResolvedValueOnce({
          type: 'archived',
          exists: false
        });

      const result = await pathAnalyzer.analyzeSinglePath('AW001-AUD-***********', {}, null);

      expect(result.input).toBe('AW001-AUD-***********');
      expect(result.auditType).toBe('AUD-SA');
      expect(result.year).toBe('2025');
      expect(result.possibleLocations).toHaveLength(2);
      expect(result.foundLocations).toHaveLength(1);
      expect(result.foundLocations[0].type).toBe('primary');
      expect(result.totalSizeInMB).toBe(100);
      expect(result.totalFileCount).toBe(50);
      expect(result.allLongNamesFiles).toEqual(['report1.txt']);
    });
  });

  describe('generateLongNamesReport', () => {
    test('should generate table format report with both name and path violations', () => {
      const longItems = [
        {
          name: 'very-long-filename.txt',
          fullPath: 'H:\\very\\long\\path\\' + 'a'.repeat(300) + '\\very-long-filename.txt',
          type: 'file',
          nameLength: 20,
          pathLength: 350,
          isNameTooLong: false,
          isPathTooLong: true,
          violationType: 'path'
        },
        {
          name: 'b'.repeat(260),
          fullPath: 'H:\\short\\path\\' + 'b'.repeat(260),
          type: 'folder',
          nameLength: 260,
          pathLength: 280,
          isNameTooLong: true,
          isPathTooLong: true,
          violationType: 'both'
        }
      ];

      const report = pathAnalyzer.generateLongNamesReport(longItems, 'H:\\test\\path', 250);

      expect(report).toContain('Long Names/Paths Report');
      expect(report).toContain('| No. of Characters | Full Path');
      expect(report).toContain('| Type     | Violation |');
      expect(report).toContain('|               350 |');
      expect(report).toContain('|               280 |');
      expect(report).toContain('| file   | path      |');
      expect(report).toContain('| folder | both      |');
      expect(report).toContain('SUMMARY:');
      expect(report).toContain('Total Items: 2');
      expect(report).toContain('- Files: 1');
      expect(report).toContain('- Folders: 1');
      expect(report).toContain('- Name too long: 1');
      expect(report).toContain('- Path too long: 2');
      expect(report).toContain('- Both name and path: 1');
      expect(report).toContain('CSV FORMAT');
      expect(report).toContain('"Character Count","Full Path","Type","Violation Type"');
    });

    test('should sort items by path length (longest first)', () => {
      const longItems = [
        {
          pathLength: 260,
          fullPath: 'shorter-path',
          type: 'file',
          violationType: 'path'
        },
        {
          pathLength: 300,
          fullPath: 'longer-path',
          type: 'file',
          violationType: 'path'
        }
      ];

      const report = pathAnalyzer.generateLongNamesReport(longItems, 'test-path', 250);

      const longerPathIndex = report.indexOf('longer-path');
      const shorterPathIndex = report.indexOf('shorter-path');
      
      expect(longerPathIndex).toBeLessThan(shorterPathIndex);
    });
  });

  describe('checkLongNames', () => {
    test('should return null when no long names found', async () => {
      // Mock fs.readdir to return empty directory
      require('fs-extra').readdir = jest.fn().mockResolvedValue([]);

      const result = await pathAnalyzer.checkLongNames('test-path', 'report.txt', null);
      expect(result).toBeNull();
    });

    test('should emit SSE progress events', async () => {
      require('fs-extra').readdir = jest.fn().mockResolvedValue([]);

      await pathAnalyzer.checkLongNames('test-path', 'report.txt', 'test-session');

      expect(mockSSEManager.broadcast).toHaveBeenCalledWith('test-session', {
        type: 'progress',
        data: {
          stage: 'checking_long_names',
          message: 'Scanning for names and paths over 250 characters'
        }
      });
    });
  });

  describe('analyzeAuditPaths', () => {
    test('should process multiple inputs and return summary', async () => {
      // Mock analyzeSinglePath
      pathAnalyzer.analyzeSinglePath = jest.fn()
        .mockResolvedValueOnce({
          sizeInMB: 100,
          fileCount: 50
        })
        .mockResolvedValueOnce({
          sizeInMB: 200,
          fileCount: 75
        });

      const result = await pathAnalyzer.analyzeAuditPaths(
        ['AW001-AUD-***********', 'AY002-AUD-***********'],
        {},
        'test-session'
      );

      expect(result.results).toHaveLength(2);
      expect(result.summary.totalSizeInMB).toBe(300);
      expect(result.summary.totalFiles).toBe(125);
      expect(result.summary.processedCount).toBe(2);
      expect(result.summary.successCount).toBe(2);
      expect(result.summary.errorCount).toBe(0);

      // Should emit start and complete events
      expect(mockSSEManager.broadcast).toHaveBeenCalledWith('test-session', 
        expect.objectContaining({ type: 'start' }));
      expect(mockSSEManager.broadcast).toHaveBeenCalledWith('test-session',
        expect.objectContaining({ type: 'complete' }));
    });

    test('should handle errors gracefully', async () => {
      pathAnalyzer.analyzeSinglePath = jest.fn()
        .mockResolvedValueOnce({ sizeInMB: 100, fileCount: 50 })
        .mockRejectedValueOnce(new Error('Test error'));

      const result = await pathAnalyzer.analyzeAuditPaths(
        ['AW001-AUD-***********', 'INVALID-NAME'],
        {},
        null
      );

      expect(result.results).toHaveLength(2);
      expect(result.summary.successCount).toBe(1);
      expect(result.summary.errorCount).toBe(1);
      expect(result.results[1].error).toBe('Test error');
    });
  });
});
