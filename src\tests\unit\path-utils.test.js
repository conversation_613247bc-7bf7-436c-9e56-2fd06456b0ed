/**
 * Unit tests for path utilities
 */

import { describe, test, expect, beforeEach } from '@jest/globals';
import {
  extractYear,
  getAuditType,
  getFirstCharacterMapping,
  getSecondCharacter,
  buildSourcePath,
  buildTargetPath,
  extractCompanyCode,
  validateAuditFolderName,
  generateLongNamesFilename,
  isNameTooLong,
  getPathComponents,
  getAllPossiblePaths,
  batchGetPathComponents
} from '../../shared/path-utils.js';

describe('Path Utils', () => {
  describe('extractYear', () => {
    test('should extract year from AUD-SA folder name', () => {
      expect(extractYear('AW001-AUD-***********')).toBe('2025');
      expect(extractYear('AY0138-AUD-SA-20241231')).toBe('2024');
    });

    test('should extract year from AUD-SAL folder name', () => {
      expect(extractYear('ES0083JL-AUD-SAL-20241231')).toBe('2024');
    });

    test('should return default year if no year found', () => {
      expect(extractYear('INVALID-NAME')).toBe('2025');
    });
  });

  describe('getAuditType', () => {
    test('should identify AUD-SA type', () => {
      expect(getAuditType('AW001-AUD-***********')).toBe('AUD-SA');
    });

    test('should identify AUD-SAL type', () => {
      expect(getAuditType('ES0083JL-AUD-SAL-20241231')).toBe('AUD-SAL');
    });

    test('should throw error for invalid format', () => {
      expect(() => getAuditType('INVALID-NAME')).toThrow('Invalid audit folder name format');
    });
  });

  describe('getFirstCharacterMapping', () => {
    test('should map first characters correctly', () => {
      expect(getFirstCharacterMapping('AW001-AUD-***********')).toBe('AC');
      expect(getFirstCharacterMapping('EW001-AUD-***********')).toBe('E');
      expect(getFirstCharacterMapping('FW001-AUD-***********')).toBe('FC');
    });

    test('should throw error for unmapped character', () => {
      expect(() => getFirstCharacterMapping('XW001-AUD-***********')).toThrow('No character mapping found');
    });
  });

  describe('getSecondCharacter', () => {
    test('should extract second character', () => {
      expect(getSecondCharacter('AW001-AUD-***********')).toBe('W');
      expect(getSecondCharacter('AB001-AUD-***********')).toBe('B');
    });
  });

  describe('buildSourcePath', () => {
    test('should build AUD-SA source path', () => {
      const expected = 'H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********';
      expect(buildSourcePath('AW001-AUD-***********')).toBe(expected);
    });

    test('should build AUD-SAL source path', () => {
      const expected = 'H:\\FILES\\2_AUDIT DEPT\\Year 2024\\Listed\\E\\ES0083JL';
      expect(buildSourcePath('ES0083JL-AUD-SAL-20241231')).toBe(expected);
    });
  });

  describe('buildTargetPath', () => {
    test('should build AUD-SA target path', () => {
      const expected = 'H:\\FILES\\2_AUDIT DEPT\\Lock 2025\\AC\\W\\AW001-AUD-***********';
      expect(buildTargetPath('AW001-AUD-***********')).toBe(expected);
    });

    test('should build AUD-SAL target path', () => {
      const expected = 'H:\\FILES\\2_AUDIT DEPT\\Lock 2024\\Listed\\E\\ES0083JL';
      expect(buildTargetPath('ES0083JL-AUD-SAL-20241231')).toBe(expected);
    });
  });

  describe('extractCompanyCode', () => {
    test('should extract company code from AUD-SAL folder', () => {
      expect(extractCompanyCode('ES0083JL-AUD-SAL-20241231')).toBe('ES0083JL');
    });

    test('should throw error for invalid format', () => {
      expect(() => extractCompanyCode('INVALID-NAME')).toThrow('Cannot extract company code');
    });
  });

  describe('validateAuditFolderName', () => {
    test('should validate correct AUD-SA format', () => {
      expect(validateAuditFolderName('AW001-AUD-***********')).toBe(true);
      expect(validateAuditFolderName('AY0138-AUD-SA-20241231')).toBe(true);
    });

    test('should validate correct AUD-SAL format', () => {
      expect(validateAuditFolderName('ES0083JL-AUD-SAL-20241231')).toBe(true);
    });

    test('should reject invalid formats', () => {
      expect(validateAuditFolderName('INVALID-NAME')).toBe(false);
      expect(validateAuditFolderName('AW001-INVALID-20250331')).toBe(false);
      expect(validateAuditFolderName('A-AUD-***********')).toBe(false);
    });
  });

  describe('generateLongNamesFilename', () => {
    test('should generate filename with timestamp', () => {
      const filename = generateLongNamesFilename('AW001-AUD-***********');
      expect(filename).toMatch(/^AW001-AUD-***********-\d{8}\.txt$/);
    });
  });

  describe('isNameTooLong', () => {
    test('should detect long names', () => {
      const longName = 'a'.repeat(251);
      const shortName = 'a'.repeat(100);
      
      expect(isNameTooLong(longName)).toBe(true);
      expect(isNameTooLong(shortName)).toBe(false);
    });

    test('should use custom threshold', () => {
      const name = 'a'.repeat(100);
      
      expect(isNameTooLong(name, 50)).toBe(true);
      expect(isNameTooLong(name, 150)).toBe(false);
    });
  });

  describe('getPathComponents', () => {
    test('should return complete path components for valid AUD-SA input', () => {
      const result = getPathComponents('AW001-AUD-***********');
      
      expect(result.valid).toBe(true);
      expect(result.input).toBe('AW001-AUD-***********');
      expect(result.auditType).toBe('AUD-SA');
      expect(result.year).toBe('2025');
      expect(result.sourcePath).toContain('H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W');
      expect(result.targetPath).toContain('H:\\FILES\\2_AUDIT DEPT\\Lock 2025\\AC\\W');
      expect(result.longNamesFile).toMatch(/^AW001-AUD-***********-\d{8}\.txt$/);
    });

    test('should return complete path components for valid AUD-SAL input', () => {
      const result = getPathComponents('ES0083JL-AUD-SAL-20241231');
      
      expect(result.valid).toBe(true);
      expect(result.input).toBe('ES0083JL-AUD-SAL-20241231');
      expect(result.auditType).toBe('AUD-SAL');
      expect(result.year).toBe('2024');
      expect(result.sourcePath).toContain('H:\\FILES\\2_AUDIT DEPT\\Year 2024\\Listed\\E');
      expect(result.targetPath).toContain('H:\\FILES\\2_AUDIT DEPT\\Lock 2024\\Listed\\E');
    });

    test('should return error for invalid input', () => {
      const result = getPathComponents('INVALID-NAME');
      
      expect(result.valid).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.input).toBe('INVALID-NAME');
    });
  });

  describe('getAllPossiblePaths', () => {
    test('should return multiple path variations for AUD-SA input', () => {
      const results = getAllPossiblePaths('AW001-AUD-***********');
      
      expect(results).toBeInstanceOf(Array);
      expect(results.length).toBeGreaterThan(1);
      
      // Should have primary path
      const primaryPath = results.find(r => r.type === 'primary');
      expect(primaryPath).toBeDefined();
      expect(primaryPath.valid).toBe(true);
      expect(primaryPath.priority).toBe(1);
      
      // Should have alternative year paths
      const altYearPaths = results.filter(r => r.type === 'alternative_year');
      expect(altYearPaths.length).toBeGreaterThan(0);
      
      // Should have archived/completed paths
      const archivedPath = results.find(r => r.type === 'archived');
      expect(archivedPath).toBeDefined();
      
      const completedPath = results.find(r => r.type === 'completed');
      expect(completedPath).toBeDefined();
    });

    test('should return multiple path variations for AUD-SAL input', () => {
      const results = getAllPossiblePaths('ES0083JL-AUD-SAL-20241231');
      
      expect(results).toBeInstanceOf(Array);
      expect(results.length).toBeGreaterThan(1);
      
      // Should have primary path
      const primaryPath = results.find(r => r.type === 'primary');
      expect(primaryPath).toBeDefined();
      expect(primaryPath.auditType).toBe('AUD-SAL');
      
      // Should have non-listed variation
      const nonListedPath = results.find(r => r.type === 'non_listed');
      expect(nonListedPath).toBeDefined();
    });

    test('should sort paths by priority', () => {
      const results = getAllPossiblePaths('AW001-AUD-***********');
      
      // Check that paths are sorted by priority (ascending)
      for (let i = 1; i < results.length; i++) {
        expect(results[i].priority).toBeGreaterThanOrEqual(results[i-1].priority);
      }
    });

    test('should handle invalid input gracefully', () => {
      const results = getAllPossiblePaths('INVALID-NAME');
      
      expect(results).toBeInstanceOf(Array);
      expect(results.length).toBe(1);
      expect(results[0].valid).toBe(false);
      expect(results[0].type).toBe('error');
    });
  });

  describe('batchGetPathComponents', () => {
    test('should process multiple inputs', () => {
      const inputs = [
        'AW001-AUD-***********',
        'ES0083JL-AUD-SAL-20241231',
        'INVALID-NAME'
      ];
      
      const results = batchGetPathComponents(inputs);
      
      expect(results).toHaveLength(3);
      expect(results[0].valid).toBe(true);
      expect(results[1].valid).toBe(true);
      expect(results[2].valid).toBe(false);
    });

    test('should handle empty input array', () => {
      const results = batchGetPathComponents([]);
      expect(results).toHaveLength(0);
    });
  });
});
