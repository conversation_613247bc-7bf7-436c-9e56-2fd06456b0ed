/**
 * Main entry point for Audit Path Analyzer MCP Server
 */

import { readFileSync } from 'fs';
import { AuditPathAnalyzerServer } from './mcp-server.js';
import { LOG_LEVELS } from '../shared/constants.js';

// Load configuration
let config = {};
try {
  config = JSON.parse(readFileSync('./config/mcp-config.json', 'utf8'));
} catch (error) {
  console.error('Failed to load configuration:', error.message);
  console.error('Using default configuration');
}

// Configure logging level from environment or config
const logLevel = process.env.LOG_LEVEL || config.server?.logLevel || LOG_LEVELS.INFO;
const port = process.env.PORT || config.server?.port || 3001;

// Create and start server
const server = new AuditPathAnalyzerServer({
  logLevel,
  ...config.server
});

// Handle graceful shutdown
const gracefulShutdown = async (signal) => {
  console.log(`\nReceived ${signal}. Gracefully shutting down...`);
  
  try {
    await server.stop();
    console.log('Server stopped successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
};

// Register signal handlers
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

// Start server
async function main() {
  try {
    console.log('Starting Audit Path Analyzer MCP Server...');
    console.log(`Configuration: ${JSON.stringify({ logLevel, port }, null, 2)}`);
    
    await server.start(port);
    
    console.log('Server started successfully');
    console.log('Press Ctrl+C to stop the server');
    
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Only start if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { server, AuditPathAnalyzerServer };
