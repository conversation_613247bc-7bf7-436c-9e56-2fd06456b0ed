# Audit Path Analyzer - Operation Guide

## Overview

The Audit Path Analyzer is an MCP (Model Context Protocol) server with SSE (Server-Sent Events) functionality designed for analyzing and managing audit department file paths and operations.

## Installation

### Prerequisites
- Node.js 18.0.0 or higher
- npm or yarn package manager
- Access to audit file directories (H:\FILES\2_AUDIT DEPT\)

### Setup
1. Clone/extract the project files
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure paths in `config/path-mappings.json` if needed
4. Create output directories:
   ```bash
   mkdir -p output/reports output/long-names
   ```

## Configuration

### Path Mappings (`config/path-mappings.json`)
- **basePaths**: Configure source and target base paths for different audit types
- **characterMappings**: Define first character to directory mappings
- **longNameThreshold**: Set maximum filename/foldername length (default: 250)

### MCP Configuration (`config/mcp-config.json`)
- **server**: Server settings (name, version, transport, port)
- **sse**: SSE connection settings (heartbeat, timeout, max connections)
- **tools**: Available MCP tools configuration

## Starting the Server

### Method 1: Direct Start
```bash
npm start
```

### Method 2: Development Mode (with auto-reload)
```bash
npm run dev
```

### Method 3: With Custom Configuration
```bash
LOG_LEVEL=debug PORT=3002 npm start
```

## Available Operations

### 1. Analyze Audit Paths
**Purpose**: Analyze audit folders for existence, size, file count, and long names

**MCP Tool**: `analyze_audit_paths`

**Parameters**:
- `inputs` (required): Array of audit folder names
- `operations` (optional): Object specifying which operations to perform
  - `checkPaths`: Check if paths exist (default: true)
  - `analyzeSizes`: Calculate folder sizes (default: true) 
  - `countFiles`: Count files in folders (default: true)
  - `findLongNames`: Find long file/folder names (default: true)
- `sessionId` (optional): SSE session ID for progress updates

**Example**:
```json
{
  "inputs": ["AW001-AUD-***********", "ES0083JL-AUD-SAL-20241231"],
  "operations": {
    "checkPaths": true,
    "analyzeSizes": true,
    "countFiles": true,
    "findLongNames": true
  },
  "sessionId": "session-123"
}
```

**Output**:
```json
{
  "results": [
    {
      "input": "AW001-AUD-***********",
      "auditType": "AUD-SA",
      "year": "2025",
      "possibleLocations": [
        {
          "type": "primary",
          "description": "Primary location based on naming convention"
        },
        {
          "type": "archived",
          "description": "Archived location"
        }
      ],
      "foundLocations": [
        {
          "type": "primary",
          "sourcePath": "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********",
          "targetPath": "H:\\FILES\\2_AUDIT DEPT\\Lock 2025\\AC\\W\\AW001-AUD-***********",
          "exists": true,
          "sizeInMB": 1234.56,
          "fileCount": 150,
          "fullPathsList": [
            "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********\\1_Assurance",
            "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********\\2_Non-Assurance",
            "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********\\3_Management Letter",
            "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********\\1_Assurance\\Planning"
          ],
          "lastModified": "2025-01-07T09:00:00.000Z",
          "permissions": {
            "readable": true,
            "writable": true
          },
          "longNamesFile": "AW001-AUD-***********-primary-20250107.txt"
        }
      ],
      "totalSizeInMB": 1234.56,
      "totalFileCount": 150,
      "allLongNamesFiles": ["AW001-AUD-***********-primary-20250107.txt"]
    }
  ],
  "summary": {
    "totalSizeInMB": 2468.90,
    "totalFiles": 300,
    "processedCount": 2,
    "successCount": 2,
    "errorCount": 0,
    "totalLocationsFound": 3
  }
}
```

### 2. Copy Audit Folders
**Purpose**: Copy audit folders from source to target locations

**MCP Tool**: `copy_audit_folders`

**Parameters**:
- `inputs` (required): Array of audit folder names to copy
- `deleteOriginal` (optional): Delete original after successful copy (default: false)
- `sessionId` (optional): SSE session ID for progress updates

**Example**:
```json
{
  "inputs": ["AW001-AUD-***********"],
  "deleteOriginal": false,
  "sessionId": "session-456"
}
```

### 3. Generate Long Names Report
**Purpose**: Generate detailed report of files/folders with names over specified length

**MCP Tool**: `generate_long_names_report`

**Parameters**:
- `inputs` (required): Array of audit folder names to check
- `maxLength` (optional): Maximum name length threshold (default: 250)
- `sessionId` (optional): SSE session ID for progress updates

### 4. Get Operation Status
**Purpose**: Check status of current or specific operations

**MCP Tool**: `get_operation_status`

**Parameters**:
- `operationId` (optional): Specific operation ID to check

## Server-Sent Events (SSE)

### Connecting to SSE
Connect to SSE endpoint to receive real-time progress updates:
```
GET http://localhost:3001/events/{sessionId}
```

### Event Types
- `start`: Operation started
- `progress`: Progress update
- `complete`: Operation completed
- `error`: Error occurred
- `heartbeat`: Keep-alive heartbeat

### Example SSE Events
```
event: start
data: {"operationId":"analysis_1641123456789","totalInputs":3}

event: progress
data: {"stage":"analyzing_paths","current":1,"total":3,"currentInput":"AW001-AUD-***********"}

event: complete
data: {"results":[...],"summary":{...}}
```

## Path Resolution Logic

### AUD-SA Folders
For folders like `AW001-AUD-***********`:

1. **Extract Components**:
   - First character: A → AC (from character mapping)
   - Second character: W
   - Year: 2025 (from date suffix)

2. **Build Paths**:
   - Source: `H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********`
   - Target: `H:\FILES\2_AUDIT DEPT\Lock 2025\AC\W\AW001-AUD-***********`

### AUD-SAL Folders  
For folders like `ES0083JL-AUD-SAL-20241231`:

1. **Extract Components**:
   - Company code: ES0083JL
   - First character: E
   - Year: 2024

2. **Build Paths**:
   - Source: `H:\FILES\2_AUDIT DEPT\Year 2024\Listed\E\ES0083JL`
   - Target: `H:\FILES\2_AUDIT DEPT\Lock 2024\Listed\E\ES0083JL`

## CLI Interface

### Basic Commands
```bash
# Interactive mode
npm run client

# Analyze with sample data
node src/client/cli-interface.js analyze --sample

# Copy with sample data  
node src/client/cli-interface.js copy --sample

# Generate long names report
node src/client/cli-interface.js long-names --sample

# Test path resolution
node src/client/cli-interface.js test-paths
```

### Advanced Options
```bash
# Analyze specific folders
node src/client/cli-interface.js analyze -i "AW001-AUD-***********,AY0138-AUD-SA-20241231"

# Copy and delete originals
node src/client/cli-interface.js copy --sample --delete-original

# Long names with custom threshold
node src/client/cli-interface.js long-names --sample --max-length 200
```

## Health Monitoring

### Health Check Endpoint
```
GET http://localhost:3001/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-07T09:00:00.000Z",
  "sseConnections": {
    "totalConnections": 2,
    "activeConnections": 2
  },
  "operations": {
    "analysis": 1,
    "fileOps": 0
  }
}
```

### Statistics Endpoint
```
GET http://localhost:3001/stats
```

## Troubleshooting

### Common Issues

1. **"Path not found" errors**
   - Verify network access to H: drive
   - Check path mapping configuration
   - Ensure audit folder names follow correct format

2. **SSE connection timeouts**
   - Check firewall settings for port 3001
   - Verify sessionId is unique and valid
   - Monitor connection heartbeat

3. **Long operation timeouts**
   - Increase timeout values in configuration
   - Monitor large folder operations via SSE
   - Consider breaking large operations into smaller batches

4. **Permission errors during copy operations**
   - Verify write permissions to target directories
   - Check available disk space
   - Ensure target directories exist or can be created

### Logging

Set log level via environment variable:
```bash
LOG_LEVEL=debug npm start
```

Available levels: debug, info, warn, error

### Performance Optimization

1. **Large Folders**:
   - Use SSE for progress monitoring
   - Consider parallel processing for multiple inputs
   - Monitor memory usage during operations

2. **Network Paths**:
   - Ensure stable network connection to file servers
   - Consider local caching for frequently accessed paths
   - Implement retry logic for transient network issues

## Security Considerations

1. **File Access**: Server requires read/write access to audit directories
2. **Network**: SSE endpoint exposed on configured port (default: 3001)
3. **Input Validation**: All inputs validated against expected patterns
4. **Path Traversal**: Path resolution restricted to configured base paths

## Maintenance

### Regular Tasks
1. Clean old report files from `output/` directories
2. Monitor disk space usage
3. Review and rotate log files
4. Update character mappings as needed

### Backup Considerations
- Configuration files (`config/`)
- Generated reports (`output/`)
- Application logs

## Support

For issues or questions:
1. Check operation logs for error details
2. Verify configuration settings
3. Test with sample data using CLI
4. Monitor SSE events for operation progress
