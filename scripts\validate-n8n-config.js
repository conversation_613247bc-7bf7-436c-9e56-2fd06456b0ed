#!/usr/bin/env node
/**
 * Validation script for n8n MCP server configuration
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

class N8nConfigValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.info = [];
  }

  /**
   * Main validation function
   */
  async validate() {
    console.log('🔍 Validating n8n MCP Server Configuration...\n');

    // Check basic requirements
    await this.checkNodeVersion();
    await this.checkProjectStructure();
    await this.checkConfigFiles();
    await this.checkN8nFiles();
    await this.checkDependencies();
    await this.checkPermissions();
    
    // Display results
    this.displayResults();
    
    // Exit with appropriate code
    process.exit(this.errors.length > 0 ? 1 : 0);
  }

  /**
   * Check Node.js version
   */
  async checkNodeVersion() {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      this.info.push(`✅ Node.js version: ${nodeVersion} (compatible)`);
    } else {
      this.errors.push(`❌ Node.js version: ${nodeVersion} (requires >= 18.0.0)`);
    }
  }

  /**
   * Check project structure
   */
  async checkProjectStructure() {
    const requiredDirs = [
      'src/n8n',
      'config',
      'output',
      'output/reports',
      'output/long-names'
    ];

    const requiredFiles = [
      'package.json',
      'src/n8n/n8n-server.js',
      'src/n8n/n8n-tool-handlers.js',
      'src/n8n/n8n-formatters.js',
      'src/n8n/n8n-error-handlers.js'
    ];

    // Check directories
    for (const dir of requiredDirs) {
      const dirPath = path.join(projectRoot, dir);
      if (await fs.pathExists(dirPath)) {
        this.info.push(`✅ Directory exists: ${dir}`);
      } else {
        this.errors.push(`❌ Missing directory: ${dir}`);
      }
    }

    // Check files
    for (const file of requiredFiles) {
      const filePath = path.join(projectRoot, file);
      if (await fs.pathExists(filePath)) {
        this.info.push(`✅ File exists: ${file}`);
      } else {
        this.errors.push(`❌ Missing file: ${file}`);
      }
    }
  }

  /**
   * Check configuration files
   */
  async checkConfigFiles() {
    await this.validateN8nMcpConfig();
    await this.validatePathMappings();
    await this.validateManifest();
  }

  /**
   * Validate n8n MCP configuration
   */
  async validateN8nMcpConfig() {
    const configPath = path.join(projectRoot, 'config/n8n-mcp-config.json');
    
    try {
      if (!(await fs.pathExists(configPath))) {
        this.errors.push('❌ Missing config/n8n-mcp-config.json');
        return;
      }

      const config = await fs.readJson(configPath);
      
      // Check required fields
      const requiredFields = ['name', 'version', 'transport', 'tools'];
      for (const field of requiredFields) {
        if (!config[field]) {
          this.errors.push(`❌ Missing field in n8n-mcp-config.json: ${field}`);
        }
      }

      // Check tools array
      if (config.tools && Array.isArray(config.tools)) {
        const expectedTools = [
          'analyze_audit_paths',
          'copy_audit_folders', 
          'generate_long_names_report',
          'get_operation_status'
        ];
        
        const toolNames = config.tools.map(tool => tool.name);
        for (const expectedTool of expectedTools) {
          if (toolNames.includes(expectedTool)) {
            this.info.push(`✅ Tool configured: ${expectedTool}`);
          } else {
            this.warnings.push(`⚠️  Missing tool configuration: ${expectedTool}`);
          }
        }
      }

      // Check n8n environment settings
      if (config.environment?.n8n) {
        this.info.push('✅ n8n environment configuration found');
        
        const n8nConfig = config.environment.n8n;
        if (n8nConfig.logLevel) {
          this.info.push(`✅ Log level: ${n8nConfig.logLevel}`);
        }
        if (n8nConfig.maxConcurrentOperations) {
          this.info.push(`✅ Max concurrent operations: ${n8nConfig.maxConcurrentOperations}`);
        }
      } else {
        this.warnings.push('⚠️  No n8n environment configuration found');
      }

      this.info.push('✅ n8n MCP configuration is valid');

    } catch (error) {
      this.errors.push(`❌ Invalid n8n-mcp-config.json: ${error.message}`);
    }
  }

  /**
   * Validate path mappings
   */
  async validatePathMappings() {
    const mappingsPath = path.join(projectRoot, 'config/path-mappings.json');
    
    try {
      if (!(await fs.pathExists(mappingsPath))) {
        this.errors.push('❌ Missing config/path-mappings.json');
        return;
      }

      const mappings = await fs.readJson(mappingsPath);
      
      // Check required sections
      if (mappings.basePaths) {
        this.info.push('✅ Base paths configuration found');
      } else {
        this.errors.push('❌ Missing basePaths in path-mappings.json');
      }

      if (mappings.characterMappings) {
        this.info.push('✅ Character mappings configuration found');
        this.info.push(`✅ Character mappings count: ${Object.keys(mappings.characterMappings).length}`);
      } else {
        this.errors.push('❌ Missing characterMappings in path-mappings.json');
      }

    } catch (error) {
      this.errors.push(`❌ Invalid path-mappings.json: ${error.message}`);
    }
  }

  /**
   * Validate n8n manifest
   */
  async validateManifest() {
    const manifestPath = path.join(projectRoot, 'n8n-manifest.json');
    
    try {
      if (!(await fs.pathExists(manifestPath))) {
        this.errors.push('❌ Missing n8n-manifest.json');
        return;
      }

      const manifest = await fs.readJson(manifestPath);
      
      // Check required fields
      const requiredFields = ['name', 'version', 'displayName', 'mcp', 'n8n'];
      for (const field of requiredFields) {
        if (!manifest[field]) {
          this.errors.push(`❌ Missing field in n8n-manifest.json: ${field}`);
        }
      }

      // Check MCP configuration
      if (manifest.mcp) {
        if (manifest.mcp.transport?.command === 'node' && 
            manifest.mcp.transport?.args?.includes('src/n8n/n8n-server.js')) {
          this.info.push('✅ MCP transport configuration correct');
        } else {
          this.warnings.push('⚠️  MCP transport configuration may be incorrect');
        }
      }

      // Check n8n node types
      if (manifest.n8n?.nodeTypes && Array.isArray(manifest.n8n.nodeTypes)) {
        this.info.push(`✅ n8n node types: ${manifest.n8n.nodeTypes.length} configured`);
        
        manifest.n8n.nodeTypes.forEach(nodeType => {
          if (nodeType.name && nodeType.displayName) {
            this.info.push(`✅ Node type: ${nodeType.displayName}`);
          }
        });
      } else {
        this.warnings.push('⚠️  No n8n node types configured');
      }

      this.info.push('✅ n8n manifest is valid');

    } catch (error) {
      this.errors.push(`❌ Invalid n8n-manifest.json: ${error.message}`);
    }
  }

  /**
   * Check n8n-specific files
   */
  async checkN8nFiles() {
    const n8nFiles = [
      'src/n8n/n8n-server.js',
      'src/n8n/n8n-tool-handlers.js',
      'src/n8n/n8n-formatters.js',
      'src/n8n/n8n-error-handlers.js'
    ];

    for (const file of n8nFiles) {
      const filePath = path.join(projectRoot, file);
      try {
        const content = await fs.readFile(filePath, 'utf8');
        
        // Basic syntax check
        if (content.includes('export class') || content.includes('export default')) {
          this.info.push(`✅ n8n file syntax OK: ${file}`);
        } else {
          this.warnings.push(`⚠️  Potential syntax issue in: ${file}`);
        }
        
        // Check for required imports/exports
        if (file.includes('n8n-server.js') && content.includes('N8nAuditPathAnalyzerServer')) {
          this.info.push('✅ n8n server class found');
        }
        
      } catch (error) {
        this.errors.push(`❌ Error reading ${file}: ${error.message}`);
      }
    }
  }

  /**
   * Check dependencies
   */
  async checkDependencies() {
    try {
      const packagePath = path.join(projectRoot, 'package.json');
      const packageJson = await fs.readJson(packagePath);
      
      // Check for required dependencies
      const requiredDeps = [
        '@modelcontextprotocol/sdk',
        'express',
        'cors',
        'fs-extra'
      ];

      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      for (const dep of requiredDeps) {
        if (allDeps[dep]) {
          this.info.push(`✅ Dependency: ${dep}@${allDeps[dep]}`);
        } else {
          this.errors.push(`❌ Missing dependency: ${dep}`);
        }
      }

      // Check for n8n scripts
      const scripts = packageJson.scripts || {};
      const expectedScripts = ['n8n:start', 'n8n:dev', 'n8n:production'];
      
      for (const script of expectedScripts) {
        if (scripts[script]) {
          this.info.push(`✅ npm script: ${script}`);
        } else {
          this.warnings.push(`⚠️  Missing npm script: ${script}`);
        }
      }

    } catch (error) {
      this.errors.push(`❌ Error checking dependencies: ${error.message}`);
    }
  }

  /**
   * Check file permissions
   */
  async checkPermissions() {
    const checkPaths = [
      'output',
      'output/reports',
      'output/long-names'
    ];

    for (const checkPath of checkPaths) {
      const fullPath = path.join(projectRoot, checkPath);
      try {
        // Try to write a test file
        const testFile = path.join(fullPath, '.write-test');
        await fs.writeFile(testFile, 'test');
        await fs.remove(testFile);
        this.info.push(`✅ Write permissions OK: ${checkPath}`);
      } catch (error) {
        this.warnings.push(`⚠️  Write permission issue: ${checkPath} - ${error.message}`);
      }
    }
  }

  /**
   * Display validation results
   */
  displayResults() {
    console.log('\n📋 Validation Results:\n');

    // Display errors
    if (this.errors.length > 0) {
      console.log('🚨 ERRORS:');
      this.errors.forEach(error => console.log(`  ${error}`));
      console.log('');
    }

    // Display warnings
    if (this.warnings.length > 0) {
      console.log('⚠️  WARNINGS:');
      this.warnings.forEach(warning => console.log(`  ${warning}`));
      console.log('');
    }

    // Display info
    if (this.info.length > 0) {
      console.log('ℹ️  INFO:');
      this.info.forEach(info => console.log(`  ${info}`));
      console.log('');
    }

    // Summary
    console.log('📊 SUMMARY:');
    console.log(`  Errors: ${this.errors.length}`);
    console.log(`  Warnings: ${this.warnings.length}`);
    console.log(`  Info items: ${this.info.length}`);
    console.log('');

    if (this.errors.length === 0) {
      console.log('🎉 n8n MCP Server configuration is valid!');
      console.log('');
      console.log('Next steps:');
      console.log('1. Start the n8n server: npm run n8n:start');
      console.log('2. Register with n8n using n8n-manifest.json');
      console.log('3. Test with sample workflows');
    } else {
      console.log('❌ Please fix the errors above before proceeding.');
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new N8nConfigValidator();
  validator.validate().catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export default N8nConfigValidator;
