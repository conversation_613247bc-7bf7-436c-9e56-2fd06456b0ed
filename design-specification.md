# Audit File Path Analyzer MCP Server - Design Specification

## Overview
A Model Context Protocol (MCP) server with Server-Sent Events (SSE) functionality for analyzing and managing audit department file paths and operations.

## Core Requirements

### 1. Path Analysis Features
- **Multiple Location Discovery**: Automatically finds audit folders across multiple possible locations (primary, archived, completed, backup)
- **Recursive Path Checking**: Check if target paths exist within given folder structures
- **Full Path Enumeration**: Returns complete list of all full paths found under each discovered source location
- **Folder Size Analysis**: Calculate folder size in MB with 2 decimal precision
- **File Count Analysis**: Count files in each given folder
- **Total Size Calculation**: Calculate total folder size across multiple directories
- **Long Path/Name Detection**: Identify folder/file names OR full paths over 250 characters with table format reports

### 2. Path Mapping Logic
- **Source Base Paths**:
  - AUD-SA: `H:\FILES\2_AUDIT DEPT\Year {currentYear}`
  - AUD-SAL: `H:\FILES\2_AUDIT DEPT\Year {currentYear}\Listed`
  
- **Target Base Paths**:
  - AUD-SA: `H:\FILES\2_AUDIT DEPT\Lock {currentYear}`
  - AUD-SAL: `H:\FILES\2_AUDIT DEPT\Lock {currentYear}\Listed`

- **First Character Mapping**:
  ```
  A -> AC, E -> E, F -> FC, G -> GC, Y -> IY, 
  J -> JC, M -> MC, P -> PW, Q -> Q, R -> RL, 
  S -> SH, T -> TO, Z -> Z
  ```

### 3. Optional Operations
- **Copy and Move**: Copy entire source path + input name to target path + input name
- **Cleanup**: Delete original folder after successful copy

## Technical Architecture

### Server Components
1. **MCP Server Core**: Handle MCP protocol communication
2. **SSE Manager**: Real-time progress updates via Server-Sent Events
3. **Path Analyzer**: Core path analysis and file operations
4. **File Operations Manager**: Handle copy/move/delete operations
5. **Report Generator**: Generate analysis reports and long name files

### Client Components
1. **MCP Client**: Connect to and communicate with MCP server
2. **SSE Client**: Receive real-time updates
3. **CLI Interface**: Command-line interface for operations
4. **Progress Display**: Real-time progress visualization

## Data Flow
1. Client sends input list to MCP server
2. Server processes each input, determining source/target paths
3. Server performs analysis operations with SSE progress updates
4. Server generates reports and handles optional copy/move operations
5. Client receives final results and generated files

## Input/Output Specification

### Input Format
```javascript
{
  "inputs": ["AW001-AUD-***********", "AY0138-AUD-***********", "ES0083JL-AUD-SAL-20241231"],
  "operations": {
    "checkPaths": true,
    "analyzeSizes": true,
    "countFiles": true,
    "findLongNames": true,
    "copyAndMove": false  // optional
  }
}
```

### Output Format
```javascript
{
  "results": [
    {
      "input": "AW001-AUD-***********",
      "auditType": "AUD-SA",
      "year": "2025",
      "possibleLocations": [
        {
          "type": "primary",
          "description": "Primary location based on naming convention"
        },
        {
          "type": "archived", 
          "description": "Archived location"
        }
      ],
      "foundLocations": [
        {
          "type": "primary",
          "sourcePath": "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********",
          "targetPath": "H:\\FILES\\2_AUDIT DEPT\\Lock 2025\\AC\\W\\AW001-AUD-***********",
          "exists": true,
          "sizeInMB": 1234.56,
          "fileCount": 150,
          "fullPathsList": [
            "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********\\1_Assurance",
            "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********\\2_Non-Assurance"
          ],
          "longNamesFile": "AW001-AUD-***********-primary-20250107.txt",
          "lastModified": "2025-01-07T09:00:00.000Z",
          "permissions": {
            "readable": true,
            "writable": true
          }
        }
      ],
      "totalSizeInMB": 1234.56,
      "totalFileCount": 150,
      "allLongNamesFiles": ["AW001-AUD-***********-primary-20250107.txt"]
    }
  ],
  "summary": {
    "totalSizeInMB": 5678.90,
    "totalFiles": 450,
    "processedCount": 3,
    "successCount": 3,
    "errorCount": 0,
    "totalLocationsFound": 4
  }
}
```

## Error Handling
- Path not found errors
- File permission errors
- Disk space validation before copy operations
- Network path availability checks
- Long operation timeout handling

## Performance Considerations
- Asynchronous operations for large file operations
- Progress reporting via SSE for long-running tasks
- Memory efficient file counting for large directories
- Parallel processing where appropriate
