# Audit Path Analyzer - Test Plan

## Overview

This document outlines the comprehensive testing strategy for the Audit Path Analyzer MCP Server, covering unit tests, integration tests, performance tests, and end-to-end testing scenarios.

## Test Objectives

1. **Functional Correctness**: Verify all features work as specified
2. **Path Resolution Accuracy**: Ensure correct path mapping logic
3. **Performance**: Validate system performance under various loads
4. **Reliability**: Test error handling and recovery scenarios
5. **Integration**: Verify MCP protocol compliance and SSE functionality
6. **Security**: Validate input sanitization and access controls

## Test Environment

### Prerequisites
- Node.js 18.0.0 or higher
- Jest testing framework
- Mock file system for unit tests
- Test audit directory structure
- Network access to test paths (or mocked equivalents)

### Test Data Sets

#### Valid Audit Folder Names
```
AUD-SA Format:
- AW001-AUD-***********
- AY0138-AUD-***********
- EW005-AUD-***********
- FG123-AUD-***********

AUD-SAL Format:
- ES0083JL-AUD-SAL-20241231
- AB1234CD-AUD-SAL-20250630
- QR9876ST-AUD-SAL-20240815
```

#### Invalid Audit Folder Names
```
- INVALID-NAME
- AW001-INVALID-20250331
- A-AUD-*********** (too short prefix)
- AW001-AUD-SA-2025 (invalid date)
- AW001-AUD-*********** (invalid date)
```

#### Test Directory Structure
```
test-data/
├── source/
│   ├── H/FILES/2_AUDIT DEPT/Year 2025/
│   │   ├── AC/W/AW001-AUD-***********/
│   │   └── E/EW005-AUD-***********/
│   └── H/FILES/2_AUDIT DEPT/Year 2024/Listed/
│       └── E/ES0083JL/
├── target/
│   └── H/FILES/2_AUDIT DEPT/Lock 2025/
└── long-names/
    └── folders-with-very-long-names-over-250-characters...
```

## Test Categories

### 1. Unit Tests

#### 1.1 Path Utilities (`src/tests/unit/path-utils.test.js`)
- **extractYear()**: Year extraction from folder names
- **getAuditType()**: Audit type identification (AUD-SA vs AUD-SAL)
- **getFirstCharacterMapping()**: Character mapping logic
- **buildSourcePath()**: Source path construction
- **buildTargetPath()**: Target path construction
- **validateAuditFolderName()**: Input validation
- **getPathComponents()**: Complete path resolution

#### 1.2 Path Analyzer (`src/tests/unit/path-analyzer.test.js`)
- **analyzeSinglePath()**: Individual path analysis with multiple location discovery
- **analyzeAuditPaths()**: Batch path analysis across multiple locations
- **findAllPossibleLocations()**: Multiple location discovery logic
- **analyzeLocation()**: Individual location analysis
- **getAllFullPaths()**: Full path enumeration under directories
- **checkPathExists()**: Path existence validation
- **analyzeFolderStats()**: Size and file count calculation
- **checkLongNames()**: Long name and path detection (>250 characters)
- **generateLongNamesReport()**: Table format report generation with CSV export

#### 1.3 SSE Manager (`src/tests/unit/sse-manager.test.js`)
- **addConnection()**: SSE connection management
- **sendEvent()**: Event transmission
- **broadcastToAll()**: Broadcasting functionality
- **formatSSEData()**: SSE data formatting
- **heartbeat management**: Connection keep-alive

#### 1.4 File Operations (`src/tests/unit/file-operations.test.js`)
- **validateAllPaths()**: Path validation
- **checkDiskSpace()**: Disk space verification
- **copySingleFolder()**: Individual folder copy
- **copyWithProgress()**: Progress tracking during copy
- **calculateDirectorySize()**: Size calculation

### 2. Integration Tests

#### 2.1 MCP Server Integration (`src/tests/integration/mcp-server.test.js`)
- **Tool Registration**: Verify all tools are properly registered
- **Tool Execution**: Test tool call handling
- **Resource Access**: Test resource read operations
- **Error Handling**: Verify error responses
- **Protocol Compliance**: MCP protocol adherence

#### 2.2 SSE Integration (`src/tests/integration/sse-integration.test.js`)
- **Connection Lifecycle**: Full SSE connection flow
- **Event Streaming**: Real-time event delivery
- **Progress Updates**: Analysis progress tracking
- **Error Broadcasting**: Error event handling
- **Connection Recovery**: Reconnection scenarios

#### 2.3 End-to-End Workflow (`src/tests/integration/end-to-end.test.js`)
- **Complete Analysis Flow**: Full analysis operation
- **Copy Operation Flow**: Complete copy workflow
- **Long Names Report**: Full report generation
- **Mixed Operations**: Multiple concurrent operations

### 3. Performance Tests

#### 3.1 Load Testing
- **Concurrent Connections**: Multiple SSE connections (up to 100)
- **Large Folder Analysis**: Folders with 10,000+ files
- **Batch Processing**: 50+ audit folders simultaneously
- **Memory Usage**: Monitor memory consumption
- **Response Times**: Measure operation completion times

#### 3.2 Stress Testing
- **Resource Exhaustion**: Maximum file operations
- **Network Interruption**: Network connectivity issues
- **Disk Space**: Low disk space scenarios
- **Long-Running Operations**: Multi-hour copy operations

### 4. Security Tests

#### 4.1 Input Validation
- **Path Traversal**: Malicious path inputs
- **SQL Injection**: Database-like injection attempts
- **XSS Prevention**: Script injection in folder names
- **Buffer Overflow**: Extremely long inputs

#### 4.2 Access Control
- **File Permissions**: Read/write permission validation
- **Directory Access**: Restricted directory access
- **Network Paths**: UNC path security

## Test Execution Strategy

### 1. Automated Testing

#### Unit Tests
```bash
# Run all unit tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test suite
npm test -- path-utils.test.js

# Watch mode for development
npm run test:watch
```

#### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run specific integration test
npm test -- mcp-server.test.js
```

### 2. Manual Testing

#### CLI Testing
```bash
# Test CLI commands
npm run client

# Test with sample data
node src/client/cli-interface.js analyze --sample
node src/client/cli-interface.js copy --sample
node src/client/cli-interface.js long-names --sample
```

#### SSE Testing
```bash
# Start server
npm start

# Connect to SSE endpoint
curl -N http://localhost:3001/events/test-session-123

# Test health endpoint
curl http://localhost:3001/health
```

### 3. Performance Testing

#### Load Test Script
```bash
# Simulate multiple concurrent operations
node scripts/load-test.js --connections 50 --operations 100
```

#### Memory Monitoring
```bash
# Monitor memory usage during operations
node --inspect src/server/index.js
```

## Test Data Management

### Test File Creation
```bash
# Create test directory structure
node scripts/create-test-data.js

# Generate long filename test cases
node scripts/generate-long-names.js
```

### Mock Data
- Mock file system operations for unit tests
- Simulated network delays for performance tests
- Generated audit folder structures for integration tests

## Expected Results

### Pass Criteria

#### Unit Tests
- **Coverage**: Minimum 90% code coverage
- **All Functions**: 100% of public methods tested
- **Edge Cases**: All boundary conditions covered
- **Error Scenarios**: All error paths tested

#### Integration Tests
- **MCP Compliance**: All MCP protocol requirements met
- **SSE Functionality**: Real-time updates working correctly
- **Data Integrity**: No data corruption during operations
- **Resource Management**: Proper cleanup of resources

#### Performance Tests
- **Response Time**: Analysis operations < 30 seconds for typical folders
- **Throughput**: Handle 10 concurrent operations
- **Memory Usage**: < 500MB for typical workloads
- **SSE Connections**: Support 100 concurrent connections

### Failure Handling
- **Graceful Degradation**: System continues operating with partial failures
- **Error Recovery**: Automatic retry for transient failures
- **Data Consistency**: No partial operations or corrupted state
- **Resource Cleanup**: Proper cleanup on failures

## Test Scenarios

### Scenario 1: Basic Path Analysis
1. **Setup**: Valid audit folder names
2. **Execute**: analyze_audit_paths tool
3. **Verify**: Correct path resolution and existence checks
4. **Expected**: All paths correctly mapped and analyzed

### Scenario 2: Large Folder Copy
1. **Setup**: Large audit folder (>1GB, >1000 files)
2. **Execute**: copy_audit_folders with SSE monitoring
3. **Verify**: Progress updates and successful copy
4. **Expected**: Complete copy with progress tracking

### Scenario 3: Long Names Detection
1. **Setup**: Folders with files/folders > 250 characters
2. **Execute**: generate_long_names_report
3. **Verify**: All long names detected and reported
4. **Expected**: Complete report with all violations

### Scenario 4: Error Handling
1. **Setup**: Invalid folder names and missing paths
2. **Execute**: Various operations
3. **Verify**: Appropriate error messages and recovery
4. **Expected**: Graceful error handling without crashes

### Scenario 5: Concurrent Operations
1. **Setup**: Multiple simultaneous requests
2. **Execute**: Parallel analysis and copy operations
3. **Verify**: All operations complete correctly
4. **Expected**: No conflicts or data corruption

### Scenario 6: SSE Real-time Updates
1. **Setup**: Long-running operation with SSE client
2. **Execute**: Copy operation with progress monitoring
3. **Verify**: Real-time progress updates received
4. **Expected**: Accurate progress reporting

## Test Automation

### Continuous Integration
```yaml
# GitHub Actions workflow
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm test
      - run: npm run test:integration
      - run: npm run test:performance
```

### Test Reporting
- **Coverage Reports**: HTML and JSON coverage reports
- **Performance Metrics**: Response time and memory usage graphs
- **Error Analysis**: Detailed error logs and stack traces
- **Test Results**: JUnit XML format for CI integration

## Test Maintenance

### Regular Activities
1. **Update Test Data**: Refresh with new audit folder patterns
2. **Performance Baselines**: Update performance expectations
3. **Test Coverage**: Maintain high coverage as code evolves
4. **Mock Updates**: Keep mocks synchronized with real APIs

### Test Environment Maintenance
1. **Cleanup**: Regular cleanup of test artifacts
2. **Dependencies**: Keep test dependencies updated
3. **Infrastructure**: Maintain test infrastructure and tools
4. **Documentation**: Keep test documentation current

## Risk Assessment

### High Risk Areas
1. **File Operations**: Potential data loss during copy/move
2. **Path Resolution**: Incorrect path mapping leading to wrong operations
3. **Network Operations**: Failures accessing network paths
4. **Long Operations**: Timeouts and resource exhaustion

### Mitigation Strategies
1. **Backup Verification**: Verify successful copy before delete
2. **Path Validation**: Multiple validation layers for path resolution
3. **Network Resilience**: Retry logic and graceful degradation
4. **Resource Monitoring**: Active monitoring of system resources

This comprehensive test plan ensures the Audit Path Analyzer meets all functional, performance, and reliability requirements while maintaining data integrity and system stability.
