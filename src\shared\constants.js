/**
 * Application constants for Audit Path Analyzer
 */

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PATH_NOT_FOUND: 'PATH_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  DISK_FULL: 'DISK_FULL',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  MCP_PROTOCOL_ERROR: 'MCP_PROTOCOL_ERROR',
  FILE_OPERATION_ERROR: 'FILE_OPERATION_ERROR'
};

export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
};

export const OPERATION_TYPES = {
  ANALYZE_PATHS: 'analyze_paths',
  COPY_FOLDERS: 'copy_folders',
  GENERATE_REPORT: 'generate_report',
  CHECK_LONG_NAMES: 'check_long_names'
};

export const SSE_EVENTS = {
  PROGRESS: 'progress',
  COMPLETE: 'complete',
  ERROR: 'error',
  HEARTBEAT: 'heartbeat',
  START: 'start'
};

export const PROGRESS_STAGES = {
  INITIALIZING: 'initializing',
  ANALYZING_PATHS: 'analyzing_paths',
  COUNTING_FILES: 'counting_files',
  CALCULATING_SIZES: 'calculating_sizes',
  CHECKING_LONG_NAMES: 'checking_long_names',
  COPYING_FILES: 'copying_files',
  GENERATING_REPORTS: 'generating_reports',
  COMPLETED: 'completed'
};

export const DEFAULT_CONFIG = {
  MAX_CONCURRENT_OPERATIONS: 5,
  LONG_NAME_THRESHOLD: 250,
  ANALYSIS_TIMEOUT: 300000, // 5 minutes
  COPY_TIMEOUT: 7200000,    // 2 hours
  SSE_TIMEOUT: 1800000,     // 30 minutes
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
  MAX_SSE_CONNECTIONS: 100,
  CHUNK_SIZE: 1024 * 1024,  // 1MB chunks for file operations
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
};

export const FILE_PATTERNS = {
  AUDIT_FOLDER: /^[A-Z]{2,}\d{3,}-AUD-(SA|SAL)-\d{8}$/,
  YEAR_EXTRACT: /(\d{4})$/,
  LONG_NAME_FILE: /^[A-Z0-9\-]+-\d{8}\.txt$/
};

export const MIME_TYPES = {
  JSON: 'application/json',
  TEXT: 'text/plain',
  SSE: 'text/event-stream'
};

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500
};
