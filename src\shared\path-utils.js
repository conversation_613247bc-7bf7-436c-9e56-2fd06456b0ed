/**
 * Path utilities for audit file path resolution and manipulation
 */

import path from 'path';
import { readFileSync } from 'fs';
import { FILE_PATTERNS } from './constants.js';

// Load path mappings configuration
let pathMappings;
try {
  pathMappings = JSON.parse(readFileSync('./config/path-mappings.json', 'utf8'));
} catch (error) {
  console.error('Failed to load path mappings:', error);
  pathMappings = {};
}

/**
 * Extract year from audit folder name
 * @param {string} auditFolderName - e.g., "AW001-AUD-SA-20250331"
 * @returns {string} - Year extracted from the name
 */
export function extractYear(auditFolderName) {
  const match = auditFolderName.match(FILE_PATTERNS.YEAR_EXTRACT);
  if (match) {
    return match[1];
  }
  return pathMappings.defaultYear || new Date().getFullYear().toString();
}

/**
 * Determine audit type from folder name
 * @param {string} auditFolderName - e.g., "AW001-AUD-SA-20250331"
 * @returns {string} - "AUD-SA" or "AUD-SAL"
 */
export function getAuditType(auditFolderName) {
  if (auditFolderName.includes('-AUD-SAL-')) {
    return 'AUD-SAL';
  }
  if (auditFolderName.includes('-AUD-SA-')) {
    return 'AUD-SA';
  }
  throw new Error(`Invalid audit folder name format: ${auditFolderName}`);
}

/**
 * Get first character mapping for path resolution
 * @param {string} auditFolderName - e.g., "AW001-AUD-SA-20250331"
 * @returns {string} - Mapped first character (e.g., "AC" for "A")
 */
export function getFirstCharacterMapping(auditFolderName) {
  const firstChar = auditFolderName.charAt(0).toUpperCase();
  const mapping = pathMappings.characterMappings?.[firstChar];
  
  if (!mapping) {
    throw new Error(`No character mapping found for first character: ${firstChar}`);
  }
  
  return mapping;
}

/**
 * Get second character for path resolution
 * @param {string} auditFolderName - e.g., "AW001-AUD-SA-20250331"
 * @returns {string} - Second character (e.g., "W")
 */
export function getSecondCharacter(auditFolderName) {
  return auditFolderName.charAt(1).toUpperCase();
}

/**
 * Build source path for audit folder
 * @param {string} auditFolderName - e.g., "AW001-AUD-SA-20250331"
 * @returns {string} - Full source path
 */
export function buildSourcePath(auditFolderName) {
  const auditType = getAuditType(auditFolderName);
  const year = extractYear(auditFolderName);
  
  let basePath = pathMappings.basePaths?.source?.[auditType];
  if (!basePath) {
    throw new Error(`No source base path configured for audit type: ${auditType}`);
  }
  
  // Replace year placeholder
  basePath = basePath.replace('{year}', year);
  
  if (auditType === 'AUD-SA') {
    // For AUD-SA: basePath + AC + W + folderName
    const firstCharMapping = getFirstCharacterMapping(auditFolderName);
    const secondChar = getSecondCharacter(auditFolderName);
    return path.join(basePath, firstCharMapping, secondChar, auditFolderName);
  } else if (auditType === 'AUD-SAL') {
    // For AUD-SAL: basePath + E + ES0083JL (extract company code)
    const companyCode = extractCompanyCode(auditFolderName);
    const firstChar = companyCode.charAt(0).toUpperCase();
    return path.join(basePath, firstChar, companyCode);
  }
  
  throw new Error(`Unsupported audit type: ${auditType}`);
}

/**
 * Build target path for audit folder
 * @param {string} auditFolderName - e.g., "AW001-AUD-SA-20250331"
 * @returns {string} - Full target path
 */
export function buildTargetPath(auditFolderName) {
  const auditType = getAuditType(auditFolderName);
  const year = extractYear(auditFolderName);
  
  let basePath = pathMappings.basePaths?.target?.[auditType];
  if (!basePath) {
    throw new Error(`No target base path configured for audit type: ${auditType}`);
  }
  
  // Replace year placeholder
  basePath = basePath.replace('{year}', year);
  
  if (auditType === 'AUD-SA') {
    // For AUD-SA: basePath + AC + W + folderName
    const firstCharMapping = getFirstCharacterMapping(auditFolderName);
    const secondChar = getSecondCharacter(auditFolderName);
    return path.join(basePath, firstCharMapping, secondChar, auditFolderName);
  } else if (auditType === 'AUD-SAL') {
    // For AUD-SAL: basePath + E + ES0083JL (extract company code)
    const companyCode = extractCompanyCode(auditFolderName);
    const firstChar = companyCode.charAt(0).toUpperCase();
    return path.join(basePath, firstChar, companyCode);
  }
  
  throw new Error(`Unsupported audit type: ${auditType}`);
}

/**
 * Extract company code from AUD-SAL folder name
 * @param {string} auditFolderName - e.g., "ES0083JL-AUD-SAL-20241231"
 * @returns {string} - Company code (e.g., "ES0083JL")
 */
export function extractCompanyCode(auditFolderName) {
  const parts = auditFolderName.split('-AUD-SAL-');
  if (parts.length === 2) {
    return parts[0];
  }
  throw new Error(`Cannot extract company code from: ${auditFolderName}`);
}

/**
 * Validate audit folder name format
 * @param {string} auditFolderName - Folder name to validate
 * @returns {boolean} - True if valid format
 */
export function validateAuditFolderName(auditFolderName) {
  return FILE_PATTERNS.AUDIT_FOLDER.test(auditFolderName);
}

/**
 * Generate long names report filename
 * @param {string} auditFolderName - Base audit folder name
 * @returns {string} - Filename for long names report
 */
export function generateLongNamesFilename(auditFolderName) {
  const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  return `${auditFolderName}-${timestamp}.txt`;
}

/**
 * Normalize path for Windows
 * @param {string} inputPath - Path to normalize
 * @returns {string} - Normalized path
 */
export function normalizePath(inputPath) {
  return path.normalize(inputPath).replace(/\//g, '\\');
}

/**
 * Check if a file/folder name exceeds the length threshold
 * @param {string} name - File or folder name
 * @param {number} threshold - Length threshold (default from config)
 * @returns {boolean} - True if name is too long
 */
export function isNameTooLong(name, threshold = pathMappings.longNameThreshold || 250) {
  return name.length > threshold;
}

/**
 * Get all path components for an audit folder
 * @param {string} auditFolderName - Audit folder name
 * @returns {object} - Object containing all path information
 */
export function getPathComponents(auditFolderName) {
  try {
    if (!validateAuditFolderName(auditFolderName)) {
      throw new Error(`Invalid audit folder name format: ${auditFolderName}`);
    }

    const auditType = getAuditType(auditFolderName);
    const year = extractYear(auditFolderName);
    const sourcePath = buildSourcePath(auditFolderName);
    const targetPath = buildTargetPath(auditFolderName);
    const longNamesFile = generateLongNamesFilename(auditFolderName);

    return {
      input: auditFolderName,
      auditType,
      year,
      sourcePath: normalizePath(sourcePath),
      targetPath: normalizePath(targetPath),
      longNamesFile,
      valid: true
    };
  } catch (error) {
    return {
      input: auditFolderName,
      error: error.message,
      valid: false
    };
  }
}

/**
 * Generate all possible path variations for an audit folder
 * @param {string} auditFolderName - Audit folder name
 * @returns {object[]} - Array of possible path configurations
 */
export function getAllPossiblePaths(auditFolderName) {
  const paths = [];
  
  try {
    const baseComponents = getPathComponents(auditFolderName);
    if (!baseComponents.valid) {
      return [baseComponents];
    }

    // Add primary path
    paths.push({
      ...baseComponents,
      type: 'primary',
      description: 'Primary location based on naming convention',
      priority: 1
    });

    const auditType = baseComponents.auditType;
    const year = parseInt(baseComponents.year);

    // Generate alternative year paths
    const alternativeYears = [year - 1, year + 1, new Date().getFullYear()];
    
    alternativeYears.forEach(altYear => {
      if (altYear !== year) {
        try {
          const altInput = auditFolderName.replace(/\d{4}/, altYear.toString());
          const altComponents = getPathComponents(altInput);
          if (altComponents.valid) {
            paths.push({
              ...altComponents,
              type: 'alternative_year',
              description: `Alternative year: ${altYear}`,
              originalYear: year.toString(),
              priority: 2
            });
          }
        } catch (error) {
          // Skip invalid alternative paths
        }
      }
    });

    // For AUD-SA, add variations
    if (auditType === 'AUD-SA') {
      // Direct path without character mapping subdirectories
      const basePath = pathMappings.basePaths?.source?.[auditType]?.replace('{year}', baseComponents.year);
      if (basePath) {
        const directSourcePath = path.join(basePath, auditFolderName);
        const directTargetPath = directSourcePath.replace('Year', 'Lock');
        
        paths.push({
          input: auditFolderName,
          auditType,
          year: baseComponents.year,
          sourcePath: normalizePath(directSourcePath),
          targetPath: normalizePath(directTargetPath),
          longNamesFile: generateLongNamesFilename(auditFolderName),
          type: 'direct',
          description: 'Direct path without character mapping subdirectories',
          priority: 3,
          valid: true
        });
      }
    }

    // For AUD-SAL, add non-Listed variation
    if (auditType === 'AUD-SAL') {
      const basePath = pathMappings.basePaths?.source?.['AUD-SA']?.replace('{year}', baseComponents.year);
      if (basePath) {
        const companyCode = extractCompanyCode(auditFolderName);
        const firstChar = companyCode.charAt(0).toUpperCase();
        const nonListedSourcePath = path.join(basePath, firstChar, companyCode);
        const nonListedTargetPath = nonListedSourcePath.replace('Year', 'Lock');
        
        paths.push({
          input: auditFolderName,
          auditType,
          year: baseComponents.year,
          sourcePath: normalizePath(nonListedSourcePath),
          targetPath: normalizePath(nonListedTargetPath),
          longNamesFile: generateLongNamesFilename(auditFolderName),
          type: 'non_listed',
          description: 'Non-Listed directory variation',
          priority: 3,
          valid: true
        });
      }
    }

    // Add archived locations
    const archivedSourcePath = baseComponents.sourcePath.replace('Year', 'Archive');
    const archivedTargetPath = baseComponents.targetPath.replace('Lock', 'Archive_Lock');
    
    paths.push({
      input: auditFolderName,
      auditType,
      year: baseComponents.year,
      sourcePath: archivedSourcePath,
      targetPath: archivedTargetPath,
      longNamesFile: generateLongNamesFilename(auditFolderName),
      type: 'archived',
      description: 'Archived location',
      priority: 4,
      valid: true
    });

    // Add completed locations
    const completedSourcePath = baseComponents.sourcePath.replace('Year', 'Completed');
    const completedTargetPath = baseComponents.targetPath.replace('Lock', 'Completed_Lock');
    
    paths.push({
      input: auditFolderName,
      auditType,
      year: baseComponents.year,
      sourcePath: completedSourcePath,
      targetPath: completedTargetPath,
      longNamesFile: generateLongNamesFilename(auditFolderName),
      type: 'completed',
      description: 'Completed location',
      priority: 4,
      valid: true
    });

    // Add backup/temporary locations
    const backupSourcePath = baseComponents.sourcePath.replace('Year', 'Backup');
    const backupTargetPath = baseComponents.targetPath.replace('Lock', 'Backup_Lock');
    
    paths.push({
      input: auditFolderName,
      auditType,
      year: baseComponents.year,
      sourcePath: backupSourcePath,
      targetPath: backupTargetPath,
      longNamesFile: generateLongNamesFilename(auditFolderName),
      type: 'backup',
      description: 'Backup location',
      priority: 5,
      valid: true
    });

    // Sort by priority
    paths.sort((a, b) => a.priority - b.priority);

    return paths;

  } catch (error) {
    return [{
      input: auditFolderName,
      error: error.message,
      valid: false,
      type: 'error',
      description: 'Failed to generate paths'
    }];
  }
}

/**
 * Batch process multiple audit folder names
 * @param {string[]} auditFolderNames - Array of audit folder names
 * @returns {object[]} - Array of path components for each folder
 */
export function batchGetPathComponents(auditFolderNames) {
  return auditFolderNames.map(name => getPathComponents(name));
}
