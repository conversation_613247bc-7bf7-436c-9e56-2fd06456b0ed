@echo off
REM Install npm modules from requirements

echo Installing npm modules...

REM Core MCP Dependencies
npm install @modelcontextprotocol/sdk@^1.0.0

REM HTTP Server & API Dependencies
npm install express@^4.18.2
npm install cors@^2.8.5

REM File System Operations
npm install fs-extra@^11.2.0
npm install path@^0.12.7

REM CLI & Terminal Dependencies
npm install chalk@^5.3.0
npm install commander@^11.1.0
npm install inquirer@^9.2.12
npm install progress@^2.0.3

REM Development Dependencies
npm install --save-dev jest@^29.7.0
npm install --save-dev nodemon@^3.0.2
npm install --save-dev eslint@^8.55.0

echo All npm modules installed successfully!
pause
