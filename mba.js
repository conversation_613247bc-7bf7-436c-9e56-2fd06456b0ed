const title = "All--[Over 50 DAYS]--The Assignment File's DOA over 50 days Report - (" + $today.toFormat('yyyy-MM-dd') + ")";

const email_content = "<p>Please be reminded that the files, based on DOA, are <span style='color: red;'>closing to 58th day</span> within 8 days, we appreciate if you could input the CC soon so that you will not miss the archive within 60 days.</p>"

const base_recipient_email = "<EMAIL>";

const cc_email = "";

const footer = `
<div class=WordSection1><p class=MsoNormal><span style='font-size:11.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal><span style='font-size:11.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal><span lang=EN-US style='font-size:11.0pt;mso-fareast-language:ZH-TW'>If you have any technical issues, please do not hesitate to contact us by email (<a href="mailto:<EMAIL>"><span style='color:blue'><EMAIL></span></a>).</span><span lang=EN-US style='mso-fareast-language:ZH-TW'><o:p></o:p></span></p><p class=MsoNormal><span lang=EN-US style='font-size:10.0pt;font-family:"Arial",sans-serif;color:black;mso-fareast-language:ZH-TW'>Thanks and best regards,</span><span lang=EN-US style='font-size:10.0pt;font-family:"Arial",sans-serif;color:black;mso-fareast-language:ZH-HK'>&nbsp; </span><span lang=EN-US style='font-size:10.0pt;color:black;mso-fareast-language:ZH-HK'><o:p></o:p></span></p><p class=MsoNormal><span lang=EN-US style='font-size:11.0pt;font-family:"Arial",sans-serif;color:#833C0B;mso-ligatures:none;mso-fareast-language:ZH-TW'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal><span lang=EN-US style='font-size:11.0pt;color:gray;mso-fareast-language:ZH-HK'><img border=0 width=48 height=28 style='width:.5in;height:.2916in' id="&#22294;&#29255;_x0020_1" src="data:image/jpeg;base64,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" alt=Small></span><b><span lang=EN-US style='font-size:16.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>C</span></b><b><span lang=EN-US style='font-size:11.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>HENG</span></b><b><span lang=EN-US style='font-size:16.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'> </span></b><b><span lang=EN-US style='font-size:11.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>&amp;</span></b><b><span lang=EN-US style='font-size:16.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'> C</span></b><b><span lang=EN-US style='font-size:11.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>HENG</span></b><b><span lang=EN-US style='font-size:16.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'> L</span></b><b><span lang=EN-US style='font-size:11.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>IMITED</span></b><span lang=EN-US style='font-size:16.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>, </span><b><i><span lang=EN-US style='font-size:8.0pt;font-family:"Times New Roman",serif;color:#632423;mso-ligatures:none;mso-fareast-language:ZH-HK'>Certified Public Accountants</span></i></b><span lang=EN-US style='mso-ligatures:none;mso-fareast-language:ZH-TW'><o:p></o:p></span></p><p class=MsoNormal><span lang=EN-US style='font-size:11.0pt;font-family:"Arial",sans-serif;color:#833C0B;mso-ligatures:none;mso-fareast-language:ZH-TW'>IT Department&nbsp;<o:p></o:p></span></p><p class=MsoNormal><span lang=EN-US style='font-size:11.0pt;color:#833C0B;mso-ligatures:none;mso-fareast-language:ZH-TW'>Levels 35 <b>[Main reception]</b> &amp; 36, Tower 1, Enterprise Square Five, 38 Wang Chiu Road, Kowloon Bay, Kowloon, Hong Kong</span><b><span lang=EN-US style='font-size:11.0pt;font-family:"Arial",sans-serif;color:#833C0B;mso-ligatures:none;mso-fareast-language:ZH-TW'><o:p></o:p></span></b></p><p class=MsoNormal><span style='font-size:11.0pt;mso-fareast-language:ZH-TW'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal><o:p>&nbsp;</o:p></p></div>
`;

const columns = Object.keys(items[0].json);

// Get the first row's JobTeam value
//const firstRowJobTeam = items[0].json.JobCompanyDefinedGroup


const table_headers = {
"CustomerInCharge": "Customer InCharge",
"JobCompanyDefinedGroup": "Job Company Defined Group",
"CustomerID": "Customer ID",
"CustomerName": "Customer Name",
"JobAssignmentID": "Job Assignment ID",
"BillingCode": "Billing Code",
"JobInchargeName": "Job Incharge Name",
"JobAssistantName": "Job Assistant Name",
"LastJobStatusCode": "Last Job Status Code",
"DOA_Count": "DOA Count",
"DOA_Date": "DOA Date",
"DOA_UpdateBy": "DOA Update By",
"JobForecastStartDate": "Job Forecast Start Date",
"JobInternalDeadLine": "Job Internal Dead Line",
"JobStatusOrderCheck": "Job Status Order Check",
"TotalInvoicedAmount": "Total Invoiced Amount",
"NumberOfInvoice": "Number of Invoice",
"LastInvoicedDate": "Last Invoiced Date",
"InvoiceData": "Invoice Data",
"EngagementPartnerID": "Engagement Partner ID",
"EngagementPartnerName": "Engagement Partner"
};

const EngagementPartnerToEmail = {
  "Alice Li": "<EMAIL>",
  "Celia Chan": "<EMAIL>",
  "Cloudy Ho": "<EMAIL>",
  "Francis Cheng": "<EMAIL>",
  "Gabriel Chan": "<EMAIL>",
  "Jason Wong": "<EMAIL>",
  "Jerry Lui": "<EMAIL>",
  "Jerry Lam": "<EMAIL>",
  "Ivan Yu": "<EMAIL>",
  "Ivan Yau": "<EMAIL>",
  "Ivy Chan": "<EMAIL>",
  "Jimmy Ngo": "<EMAIL>",
  "Michael Chan": "<EMAIL>",
  "Mr. Tong": "<EMAIL>",
  "Regan Ho": "<EMAIL>",
  "Rita Chan": "<EMAIL>",
  "Ruby Yuen": "<EMAIL>",
  "Sammy Lam": "<EMAIL>",
  "Tony Ng": "<EMAIL>",
  "null": ""
};


// Function to extract and clean email addresses from a string that may contain multiple emails separated by comma or semicolon
function extractEmails(emailString) {
  if (!emailString || typeof emailString !== 'string') {
    return [];
  }
  
  // Split by comma or semicolon, trim whitespace, and filter out empty strings
  return emailString.split(/[,;]/)
    .map(email => email.trim())
    .filter(email => email.length > 0);
}

// Get unique engagement partner names from the data
const uniquePartnerNames = [...new Set(items.map(item => item.json.EngagementPartnerName).filter(name => name && name !== "null"))];

// Get corresponding emails for the engagement partners
const partnerEmails = uniquePartnerNames.map(name => EngagementPartnerToEmail[name]).filter(email => email && email !== "");

// Extract emails from base_recipient_email (may contain multiple emails separated by comma or semicolon)
const baseEmails = extractEmails(base_recipient_email);

// Combine base emails and partner emails, then ensure all emails are unique
const allEmails = [...new Set([...baseEmails, ...partnerEmails])];
const recipient_email = allEmails.join(" , ");



const style = `
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Dynamic JSON to HTML Table Converter">
    <title>Dynamic JSON to HTML Table Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            color: #0;
            padding: 30px;
        }

        .header h1 {
            font-size: 28px;
            margin: 0 0 10px 0;
            font-weight: bold;
            color: #0;
        }

        .header p {
            font-size: 18px;
            margin: 0;
            color: #0;
        }


        .output-section {
            padding: 30px;
            min-height: 200px;
        }

        .table-container {
            border: 1px solid #e9ecef;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            background: white;
        }

        thead {
            background-color: #34495e;
        }

        th {
            background-color: #34495e;
            color: #ffffff;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #2c3e50;
        }

        tbody tr:nth-child(even),
        tbody tr.even-row {
            background-color: #e8f4f8;
        }

        tbody tr:hover {
            background-color: #d4edda;
        }

        td {
            padding: 10px 8px;
            border: 1px solid #e9ecef;
            vertical-align: top;
            font-size: 11px;
        }

    </style>
  </head>
`;

const header = `
  <thead>
    <tr>
      ${columns.map(e => '<th >' + (table_headers[e] || e) + '</th>').join('\n')}
    </tr>
  </thead>
`;

const body = `
  <tbody>
    ${items.map((e, index) => '<tr' + (index % 2 === 1 ? ' style="background-color: #e8f4f8;"' : '') + '>' + columns.map(ee => '<td>' + e.json[ee] + '</td>').join('\n') + '</tr>').join('\n')}
  </tbody>
`;

const table = `
  <table >
    ${header}
    ${body}
  </table>
`;

const html = `
<!doctype html>
<html lang="en">
  ${style}
  <body>
    <div class="container">
      <div class="header">
        <h1>Dear Partners & All,</h1>
        ${email_content}
      </div>
      <div class="output-section">
        <div class="table-container">
          ${table}
        </div>
      </div>
    </div>
    ${footer}
  </body>
</html>
`;

return [{
  json: {
    html: html,
    date: $today.toFormat('yyyy-MM-dd'),
    email: base_recipient_email,
    cc_email: cc_email,
    title: title,
    team: "T-All"
  }
}];
