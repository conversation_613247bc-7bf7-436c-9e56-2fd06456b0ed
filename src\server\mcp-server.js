/**
 * Main MCP server implementation for audit path analyzer
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import express from 'express';
import cors from 'cors';
import fs from 'fs-extra';
import path from 'path';

import { PathAnalyzer } from './path-analyzer.js';
import { SSEManager } from './sse-manager.js';
import { FileOperationsManager } from './file-operations.js';
import { Logger } from '../shared/logger.js';
import { 
  ERROR_CODES, 
  HTTP_STATUS, 
  MIME_TYPES,
  LOG_LEVELS 
} from '../shared/constants.js';

export class AuditPathAnalyzerServer {
  constructor(config = {}) {
    this.config = config;
    this.logger = new Logger(config.logLevel || LOG_LEVELS.INFO);
    this.server = new Server(
      {
        name: 'audit-path-analyzer',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          resources: {},
        },
      }
    );
    
    // Initialize components
    this.sseManager = new SSEManager(this.logger);
    this.pathAnalyzer = new PathAnalyzer(this.sseManager, this.logger);
    this.fileOperations = new FileOperationsManager(this.sseManager, this.logger);
    
    // Express app for SSE endpoint
    this.app = express();
    this.httpServer = null;
    
    this.setupServer();
    this.setupExpressApp();
  }

  /**
   * Setup MCP server handlers
   */
  setupServer() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'analyze_audit_paths',
            description: 'Analyze audit file paths with size, count, and existence checks',
            inputSchema: {
              type: 'object',
              properties: {
                inputs: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Array of audit folder names to analyze'
                },
                operations: {
                  type: 'object',
                  properties: {
                    checkPaths: { type: 'boolean', default: true },
                    analyzeSizes: { type: 'boolean', default: true },
                    countFiles: { type: 'boolean', default: true },
                    findLongNames: { type: 'boolean', default: true },
                    copyAndMove: { type: 'boolean', default: false }
                  }
                },
                sessionId: {
                  type: 'string',
                  description: 'Optional SSE session ID for progress updates'
                }
              },
              required: ['inputs']
            }
          },
          {
            name: 'copy_audit_folders',
            description: 'Copy audit folders from source to target location',
            inputSchema: {
              type: 'object',
              properties: {
                inputs: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Array of audit folder names to copy'
                },
                deleteOriginal: {
                  type: 'boolean',
                  default: false,
                  description: 'Whether to delete original after successful copy'
                },
                sessionId: {
                  type: 'string',
                  description: 'Optional SSE session ID for progress updates'
                }
              },
              required: ['inputs']
            }
          },
          {
            name: 'generate_long_names_report',
            description: 'Generate report of long file/folder names',
            inputSchema: {
              type: 'object',
              properties: {
                inputs: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Array of audit folder names to check'
                },
                maxLength: {
                  type: 'number',
                  default: 250,
                  description: 'Maximum allowed name length'
                },
                sessionId: {
                  type: 'string',
                  description: 'Optional SSE session ID for progress updates'
                }
              },
              required: ['inputs']
            }
          },
          {
            name: 'get_operation_status',
            description: 'Get status of current operations',
            inputSchema: {
              type: 'object',
              properties: {
                operationId: {
                  type: 'string',
                  description: 'Optional operation ID to check specific operation'
                }
              }
            }
          }
        ]
      };
    });

    // List available resources
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: [
          {
            uri: 'analysis_reports',
            name: 'Analysis Reports',
            description: 'List of generated analysis reports',
            mimeType: MIME_TYPES.JSON
          },
          {
            uri: 'long_names_files',
            name: 'Long Names Files',
            description: 'List of generated long names files',
            mimeType: MIME_TYPES.JSON
          },
          {
            uri: 'sse_sessions',
            name: 'SSE Sessions',
            description: 'Active SSE sessions information',
            mimeType: MIME_TYPES.JSON
          }
        ]
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'analyze_audit_paths':
            return await this.handleAnalyzeAuditPaths(args);
            
          case 'copy_audit_folders':
            return await this.handleCopyAuditFolders(args);
            
          case 'generate_long_names_report':
            return await this.handleGenerateLongNamesReport(args);
            
          case 'get_operation_status':
            return await this.handleGetOperationStatus(args);
            
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        this.logger.error(`Tool execution failed: ${name}`, { error: error.message, args });
        return {
          content: [{
            type: 'text',
            text: `Error: ${error.message}`
          }],
          isError: true
        };
      }
    });

    // Handle resource reads
    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;
      
      try {
        switch (uri) {
          case 'analysis_reports':
            return await this.getAnalysisReports();
            
          case 'long_names_files':
            return await this.getLongNamesFiles();
            
          case 'sse_sessions':
            return await this.getSSESessions();
            
          default:
            if (uri.startsWith('analysis_reports/')) {
              const reportId = uri.split('/')[1];
              return await this.getAnalysisReport(reportId);
            }
            if (uri.startsWith('long_names_files/')) {
              const filename = uri.split('/')[1];
              return await this.getLongNamesFile(filename);
            }
            throw new Error(`Unknown resource: ${uri}`);
        }
      } catch (error) {
        this.logger.error(`Resource read failed: ${uri}`, { error: error.message });
        throw error;
      }
    });
  }

  /**
   * Setup Express app for SSE endpoints
   */
  setupExpressApp() {
    this.app.use(cors());
    this.app.use(express.json());

    // SSE endpoint
    this.app.get('/events/:sessionId', (req, res) => {
      const sessionId = req.params.sessionId;
      this.sseManager.addConnection(sessionId, res, req);
    });

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        sseConnections: this.sseManager.getStats(),
        operations: {
          analysis: this.pathAnalyzer.getCurrentOperations().length,
          fileOps: this.fileOperations.getCurrentOperations().length
        }
      });
    });

    // Statistics endpoint
    this.app.get('/stats', (req, res) => {
      res.json({
        sseManager: this.sseManager.getStats(),
        pathAnalyzer: {
          currentOperations: this.pathAnalyzer.getCurrentOperations()
        },
        fileOperations: {
          currentOperations: this.fileOperations.getCurrentOperations()
        }
      });
    });
  }

  /**
   * Handle analyze audit paths tool
   */
  async handleAnalyzeAuditPaths(args) {
    const { inputs, operations = {}, sessionId } = args;
    
    if (!Array.isArray(inputs) || inputs.length === 0) {
      throw new Error('inputs must be a non-empty array');
    }

    this.logger.info('Analyzing audit paths', { inputs, operations, sessionId });
    
    const result = await this.pathAnalyzer.analyzeAuditPaths(inputs, operations, sessionId);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify(result, null, 2)
      }]
    };
  }

  /**
   * Handle copy audit folders tool
   */
  async handleCopyAuditFolders(args) {
    const { inputs, deleteOriginal = false, sessionId } = args;
    
    if (!Array.isArray(inputs) || inputs.length === 0) {
      throw new Error('inputs must be a non-empty array');
    }

    this.logger.info('Copying audit folders', { inputs, deleteOriginal, sessionId });
    
    const result = await this.fileOperations.copyAuditFolders(inputs, deleteOriginal, sessionId);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify(result, null, 2)
      }]
    };
  }

  /**
   * Handle generate long names report tool
   */
  async handleGenerateLongNamesReport(args) {
    const { inputs, maxLength = 250, sessionId } = args;
    
    if (!Array.isArray(inputs) || inputs.length === 0) {
      throw new Error('inputs must be a non-empty array');
    }

    this.logger.info('Generating long names report', { inputs, maxLength, sessionId });
    
    const operations = {
      checkPaths: true,
      analyzeSizes: false,
      countFiles: false,
      findLongNames: true
    };
    
    const result = await this.pathAnalyzer.analyzeAuditPaths(inputs, operations, sessionId);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify(result, null, 2)
      }]
    };
  }

  /**
   * Handle get operation status tool
   */
  async handleGetOperationStatus(args) {
    const { operationId } = args;
    
    if (operationId) {
      const analysisStatus = this.pathAnalyzer.getOperationStatus(operationId);
      const fileOpStatus = this.fileOperations.getOperationStatus(operationId);
      
      const status = analysisStatus || fileOpStatus;
      if (!status) {
        throw new Error(`Operation not found: ${operationId}`);
      }
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(status, null, 2)
        }]
      };
    } else {
      // Return all current operations
      const allOperations = {
        analysis: this.pathAnalyzer.getCurrentOperations(),
        fileOperations: this.fileOperations.getCurrentOperations()
      };
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(allOperations, null, 2)
        }]
      };
    }
  }

  /**
   * Get analysis reports resource
   */
  async getAnalysisReports() {
    const reportsDir = './output/reports';
    
    try {
      await fs.ensureDir(reportsDir);
      const files = await fs.readdir(reportsDir);
      const reports = files.filter(f => f.endsWith('.json'));
      
      return {
        contents: [{
          type: 'text',
          text: JSON.stringify(reports, null, 2),
          mimeType: MIME_TYPES.JSON
        }]
      };
    } catch (error) {
      return {
        contents: [{
          type: 'text',
          text: JSON.stringify({ error: error.message }, null, 2),
          mimeType: MIME_TYPES.JSON
        }]
      };
    }
  }

  /**
   * Get long names files resource
   */
  async getLongNamesFiles() {
    const longNamesDir = './output/long-names';
    
    try {
      await fs.ensureDir(longNamesDir);
      const files = await fs.readdir(longNamesDir);
      const txtFiles = files.filter(f => f.endsWith('.txt'));
      
      return {
        contents: [{
          type: 'text',
          text: JSON.stringify(txtFiles, null, 2),
          mimeType: MIME_TYPES.JSON
        }]
      };
    } catch (error) {
      return {
        contents: [{
          type: 'text',
          text: JSON.stringify({ error: error.message }, null, 2),
          mimeType: MIME_TYPES.JSON
        }]
      };
    }
  }

  /**
   * Get SSE sessions resource
   */
  async getSSESessions() {
    const sessions = {
      activeSessions: this.sseManager.getActiveSessions(),
      stats: this.sseManager.getStats()
    };
    
    return {
      contents: [{
        type: 'text',
        text: JSON.stringify(sessions, null, 2),
        mimeType: MIME_TYPES.JSON
      }]
    };
  }

  /**
   * Start the server
   */
  async start(port = 3001) {
    try {
      // Start HTTP server for SSE
      this.httpServer = this.app.listen(port, () => {
        this.logger.info(`Audit Path Analyzer server started on port ${port}`);
        this.logger.info(`SSE endpoint: http://localhost:${port}/events/{sessionId}`);
        this.logger.info(`Health check: http://localhost:${port}/health`);
      });

      // Start MCP server
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      
      this.logger.info('MCP server connected via stdio');
      
    } catch (error) {
      this.logger.error('Failed to start server:', error);
      throw error;
    }
  }

  /**
   * Stop the server
   */
  async stop() {
    this.logger.info('Stopping Audit Path Analyzer server...');
    
    // Cleanup SSE manager
    this.sseManager.cleanup();
    
    // Close HTTP server
    if (this.httpServer) {
      this.httpServer.close();
    }
    
    // Close MCP server
    await this.server.close();
    
    this.logger.info('Server stopped');
  }
}
