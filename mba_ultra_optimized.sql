-- ULTRA HIGH-PERFORMANCE MBA Query - Maximum Optimization
-- This version prioritizes speed over readability with aggressive optimizations
-- 
-- CRITICAL INDEXES REQUIRED (create these first for best performance):
-- 
-- 1. Primary performance index for DOA filtering:
CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_DOA_Ultra 
  ON JobAssignmentStatusInfo (JobStatusCode, Date_Update) 
  INCLUDE (CompanyID, JobAssignmentID, EmployeeID)
  WHERE JobStatusCode = 'DOA';

-- 2. Covering index for status lookups:
CREATE NONCLUSTERED INDEX IX_JobAssignmentStatusInfo_Status_Ultra 
  ON JobAssignmentStatusInfo (CompanyID, JobAssignmentID) 
  INCLUDE (JobStatusCode, Date_Update);

-- 3. JobAssignment performance index:
CREATE NONCLUSTERED INDEX IX_JobAssignment_Ultra 
  ON JobAssignment (CompanyID, JobAssignmentID) 
  INCLUDE (Team, CustomerID, CompleteDate, InternalDeadLine, Assistant, Incharge, BillingCode)
  WHERE (BillingCode LIKE 'ACC-%' OR BillingCode LIKE 'AUD-%' 
         OR BillingCode IN ('SPE-NT', 'SPE-DD', 'SPE-OS', 'SPE-PGT', 'SPE-SR', 'SPE-ICR', 'SPE-MAR', 'SPE-PRC'));

-- 4. Invoice performance indexes:
CREATE NONCLUSTERED INDEX IX_InvoiceDetail_Ultra 
  ON InvoiceDetail (CompanyID, SourceID, SourceType) 
  INCLUDE (UniqueID, Amount)
  WHERE SourceType = 'J';

CREATE NONCLUSTERED INDEX IX_InvoiceHead_Ultra 
  ON InvoiceHead (CompanyID, UniqueID, Status) 
  INCLUDE (ConfirmedInvoiceNumber, InvoiceDate)
  WHERE Status = 1 AND ConfirmedInvoiceNumber IS NOT NULL;

-- 5. Customer lookup index:
CREATE NONCLUSTERED INDEX IX_Customer_Ultra 
  ON Customer (CompanyID, CustomerID) 
  INCLUDE (InCharge, CompanyName_A1, CompanyName_A2, CompanyName_B1, CompanyName_B2, CompanyName_C1, CompanyName_C2);

-- MAIN ULTRA-OPTIMIZED QUERY:
WITH DOAJobs AS (
    -- Most restrictive filter first with optimal date comparison
    SELECT 
        CompanyID,
        JobAssignmentID,
        JobStatusCode,
        Date_Update,
        EmployeeID
    FROM JobAssignmentStatusInfo WITH (NOLOCK)  -- Use NOLOCK for read performance if dirty reads are acceptable
    WHERE JobStatusCode = 'DOA'
        AND Date_Update >= '2025-06-01' 
        AND Date_Update < '2026-01-01'
),

StatusAggregated AS (
    -- Single pass through status table with window functions
    SELECT 
        dj.CompanyID,
        dj.JobAssignmentID,
        dj.JobStatusCode AS DOAStatusCode,
        dj.Date_Update AS DOADate,
        dj.EmployeeID AS DOAEmployeeID,
        -- Get all status info in one pass
        MAX(CASE WHEN jsi.JobStatusCode = 'N_LOCK_CNR' THEN 1 ELSE 0 END) AS HasNLockCNR,
        MAX(CASE WHEN jsi.JobStatusCode = 'LOCKED' THEN 1 ELSE 0 END) AS HasLocked,
        MAX(CASE WHEN jsi.JobStatusCode = 'LOCKED_F' THEN 1 ELSE 0 END) AS HasLockedF,
        MAX(CASE WHEN jsi.JobStatusCode = 'CC' THEN 1 ELSE 0 END) AS HasCC,
        MAX(CASE WHEN jsi.JobStatusCode IN ('LOCKED', 'N_LOCK_CNR', 'LOCKED_F') 
                      OR jsi.JobStatusCode LIKE 'LOCKED_Z%' 
                      OR jsi.JobStatusCode LIKE 'LOCKED_S%' THEN 1 ELSE 0 END) AS HasAnyLocked,
        COUNT(CASE WHEN jsi.JobStatusCode = 'DOA' THEN 1 END) AS DOACount,
        COUNT(CASE WHEN jsi.JobStatusCode = dj.JobStatusCode THEN 1 END) AS StatusCodeCount,
        -- Latest status
        (SELECT TOP 1 JobStatusCode 
         FROM JobAssignmentStatusInfo jsi2 WITH (NOLOCK)
         WHERE jsi2.CompanyID = dj.CompanyID 
           AND jsi2.JobAssignmentID = dj.JobAssignmentID 
           AND jsi2.JobStatusCode <> ''
         ORDER BY jsi2.Date_Update DESC) AS LatestStatus
    FROM DOAJobs dj
    LEFT JOIN JobAssignmentStatusInfo jsi WITH (NOLOCK)
        ON dj.CompanyID = jsi.CompanyID 
        AND dj.JobAssignmentID = jsi.JobAssignmentID
    GROUP BY dj.CompanyID, dj.JobAssignmentID, dj.JobStatusCode, dj.Date_Update, dj.EmployeeID
),

FinalStatus AS (
    SELECT 
        *,
        -- Determine current and filter status efficiently
        CASE 
            WHEN HasNLockCNR = 1 THEN 'N_LOCK_CNR'
            WHEN HasLocked = 1 THEN 'LOCKED'
            WHEN HasLockedF = 1 THEN 'LOCKED_F'
            WHEN HasCC = 1 THEN 'CC'
            ELSE LatestStatus
        END AS CurrentStatus,
        CASE 
            WHEN HasAnyLocked = 1 THEN 'LOCKED'
            WHEN HasLockedF = 1 THEN 'LOCKED_F'
            WHEN HasCC = 1 THEN 'CC'
            ELSE LatestStatus
        END AS FilterStatus
    FROM StatusAggregated
),

InvoiceAgg AS (
    -- Optimized invoice aggregation
    SELECT 
        dj.CompanyID,
        dj.JobAssignmentID,
        COUNT(ih.ConfirmedInvoiceNumber) AS InvoiceCount,
        MAX(ih.InvoiceDate) AS LastInvoiceDate,
        SUM(id.Amount) AS TotalAmount,
        STRING_AGG(ih.ConfirmedInvoiceNumber, ', ') AS InvoiceList
    FROM DOAJobs dj
    INNER JOIN InvoiceDetail id WITH (NOLOCK)
        ON dj.CompanyID = id.CompanyID 
        AND dj.JobAssignmentID = id.SourceID
        AND id.SourceType = 'J'
    INNER JOIN InvoiceHead ih WITH (NOLOCK)
        ON id.CompanyID = ih.CompanyID 
        AND id.UniqueID = ih.UniqueID
        AND ih.Status = 1
        AND ih.ConfirmedInvoiceNumber IS NOT NULL
        AND ih.ConfirmedInvoiceNumber <> ''
    GROUP BY dj.CompanyID, dj.JobAssignmentID
)

-- Final optimized SELECT with minimal function calls
SELECT DISTINCT
    dbo.GetEmployeeNickOrFormatNameByID(c.InCharge, 1, 'A') AS InChargeOfCustomer,
    RTRIM(ja.Team) AS JobTeam,
    RTRIM(ja.CustomerID) AS CustomerID,
    CASE 
        WHEN RTRIM(ISNULL(c.CompanyName_A1, '')) <> '' 
        THEN RTRIM(c.CompanyName_A1) + ISNULL(' ' + c.CompanyName_A2, '')
        WHEN RTRIM(ISNULL(c.CompanyName_B1, '')) <> '' 
        THEN RTRIM(c.CompanyName_B1) + ISNULL(' ' + c.CompanyName_B2, '')
        ELSE RTRIM(ISNULL(c.CompanyName_C1, '')) + ISNULL(' ' + c.CompanyName_C2, '')
    END AS CustomerName,
    ja.JobAssignmentID,
    fs.CurrentStatus AS CurrentJobStatusCode,
    RTRIM(fs.DOAStatusCode) AS JobStatusCode,
    ja.CompleteDate AS JobCompleteDate,
    fs.DOADate AS JobStatusUpdatedOn,
    dbo.GetEmployeeNickOrFormatNameByID(fs.DOAEmployeeID, 1, 'A') AS JobStatusUpdateBy,
    ja.InternalDeadLine AS JobInternalDeadLine,
    CASE dbo.RBCheckCorrectJobStatusOrder(ja.CompanyID, ja.JobAssignmentID) 
        WHEN 1 THEN 'No' ELSE 'Yes' END AS JobStatusOrderCheck,
    fs.StatusCodeCount AS JobStatusCodeCount,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant, 1, 'A') AS JobAssistantName,
    dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge, 1, 'A') AS JobIncharge,
    ISNULL(inv.InvoiceCount, 0) AS NumberOfInvoice,
    ISNULL(inv.InvoiceList, '') AS JobAssignmentInvoiceList,
    inv.LastInvoiceDate AS LastInvoicedDate,
    ISNULL(inv.TotalAmount, 0) AS TotalInvoicedAmount,
    fs.DOACount AS JobStatus_DOA_Count

FROM FinalStatus fs
INNER JOIN JobAssignment ja WITH (NOLOCK)
    ON fs.CompanyID = ja.CompanyID 
    AND fs.JobAssignmentID = ja.JobAssignmentID
    AND (ja.BillingCode LIKE 'ACC-%' OR ja.BillingCode LIKE 'AUD-%' 
         OR ja.BillingCode IN ('SPE-NT', 'SPE-DD', 'SPE-OS', 'SPE-PGT', 'SPE-SR', 'SPE-ICR', 'SPE-MAR', 'SPE-PRC'))
    AND (ja.CustomerID NOT LIKE '%K' OR ja.CustomerID LIKE '%SK')
INNER JOIN Customer c WITH (NOLOCK)
    ON ja.CompanyID = c.CompanyID 
    AND ja.CustomerID = c.CustomerID
LEFT JOIN InvoiceAgg inv 
    ON fs.CompanyID = inv.CompanyID 
    AND fs.JobAssignmentID = inv.JobAssignmentID
WHERE fs.FilterStatus <> 'LOCKED'

ORDER BY 
    dbo.GetEmployeeNickOrFormatNameByID(c.InCharge, 1, 'A'), 
    RTRIM(ja.Team), 
    dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge, 1, 'A')

OPTION (RECOMPILE, MAXDOP 4);  -- Force recompilation and limit parallelism for consistency
