/**
 * n8n-specific tool handlers for enhanced workflow integration
 */

export class N8nToolHandlers {
  constructor(logger) {
    this.logger = logger;
    this.validationRules = this.setupValidationRules();
  }

  /**
   * Setup validation rules for n8n tools
   */
  setupValidationRules() {
    return {
      analyze_audit_paths: {
        maxInputs: 50,
        maxSessionIdLength: 100,
        requiredFields: ['inputs'],
        optionalFields: ['operations', 'sessionId', 'n8nContext']
      },
      copy_audit_folders: {
        maxInputs: 20,
        maxSessionIdLength: 100,
        requiredFields: ['inputs'],
        optionalFields: ['deleteOriginal', 'sessionId', 'copyOptions']
      },
      generate_long_names_report: {
        maxInputs: 30,
        maxSessionIdLength: 100,
        requiredFields: ['inputs'],
        optionalFields: ['maxLength', 'reportFormat', 'sessionId']
      },
      get_operation_status: {
        requiredFields: [],
        optionalFields: ['operationId', 'includeDetails']
      }
    };
  }

  /**
   * Validate n8n-specific arguments
   */
  validateN8nArgs(toolName, args) {
    const rules = this.validationRules[toolName];
    if (!rules) {
      throw new Error(`Unknown tool for n8n validation: ${toolName}`);
    }

    // Validate required fields
    for (const field of rules.requiredFields) {
      if (!(field in args)) {
        throw new Error(`Missing required field for n8n: ${field}`);
      }
    }

    // Validate field types and constraints
    this.validateFieldConstraints(toolName, args, rules);

    // Validate n8n context if provided
    if (args.n8nContext) {
      this.validateN8nContext(args.n8nContext);
    }

    // Validate audit folder name format for input arrays
    if (args.inputs && Array.isArray(args.inputs)) {
      this.validateAuditFolderNames(args.inputs);
    }
  }

  /**
   * Validate field constraints
   */
  validateFieldConstraints(toolName, args, rules) {
    // Validate inputs array size
    if (args.inputs && Array.isArray(args.inputs)) {
      if (rules.maxInputs && args.inputs.length > rules.maxInputs) {
        throw new Error(`Too many inputs for n8n workflow: ${args.inputs.length} > ${rules.maxInputs}`);
      }
      if (args.inputs.length === 0) {
        throw new Error('Inputs array cannot be empty for n8n workflow');
      }
    }

    // Validate session ID length
    if (args.sessionId && typeof args.sessionId === 'string') {
      if (rules.maxSessionIdLength && args.sessionId.length > rules.maxSessionIdLength) {
        throw new Error(`Session ID too long for n8n: ${args.sessionId.length} > ${rules.maxSessionIdLength}`);
      }
      if (!/^[a-zA-Z0-9_-]+$/.test(args.sessionId)) {
        throw new Error('Invalid session ID format for n8n (only alphanumeric, underscore, hyphen allowed)');
      }
    }

    // Validate maxLength for long names report
    if (toolName === 'generate_long_names_report' && args.maxLength !== undefined) {
      if (typeof args.maxLength !== 'number' || args.maxLength < 50 || args.maxLength > 1000) {
        throw new Error('maxLength must be a number between 50 and 1000 for n8n workflows');
      }
    }

    // Validate report format
    if (args.reportFormat && !['table', 'csv', 'json', 'excel'].includes(args.reportFormat)) {
      throw new Error('Invalid report format for n8n. Must be: table, csv, json, or excel');
    }
  }

  /**
   * Validate n8n context object
   */
  validateN8nContext(n8nContext) {
    if (typeof n8nContext !== 'object' || n8nContext === null) {
      throw new Error('n8nContext must be an object');
    }

    // Validate workflow ID format if provided
    if (n8nContext.workflowId && typeof n8nContext.workflowId !== 'string') {
      throw new Error('n8nContext.workflowId must be a string');
    }

    // Validate execution ID format if provided
    if (n8nContext.executionId && typeof n8nContext.executionId !== 'string') {
      throw new Error('n8nContext.executionId must be a string');
    }

    // Validate node ID format if provided
    if (n8nContext.nodeId && typeof n8nContext.nodeId !== 'string') {
      throw new Error('n8nContext.nodeId must be a string');
    }
  }

  /**
   * Validate audit folder name format
   */
  validateAuditFolderNames(inputs) {
    const auditFolderPattern = /^[A-Z]{2,8}\d*[A-Z]*-AUD-(SA|SAL)-\d{8}$/;
    
    for (const input of inputs) {
      if (typeof input !== 'string') {
        throw new Error(`Audit folder name must be a string: ${input}`);
      }
      
      if (!auditFolderPattern.test(input)) {
        throw new Error(`Invalid audit folder name format for n8n: ${input}. Expected format: AW001-AUD-SA-20250331`);
      }
    }
  }

  /**
   * Enhance arguments with n8n-specific features
   */
  enhanceArgsForN8n(args, n8nContext) {
    const enhanced = { ...args };

    // Add n8n-specific session ID if not provided
    if (!enhanced.sessionId && n8nContext.executionId) {
      enhanced.sessionId = `n8n_${n8nContext.executionId}_${Date.now()}`;
    }

    // Add n8n performance tracking
    enhanced.n8nTracking = {
      startTime: Date.now(),
      workflowId: n8nContext.workflowId,
      executionId: n8nContext.executionId,
      nodeId: n8nContext.nodeId,
      enableMetrics: true
    };

    // Enhance operations for better n8n workflow integration
    if (enhanced.operations) {
      enhanced.operations = this.enhanceOperationsForN8n(enhanced.operations);
    }

    // Add n8n-specific timeout handling
    enhanced.n8nOptions = {
      maxExecutionTime: this.getMaxExecutionTime(args),
      enableProgressCallbacks: true,
      returnDetailedErrors: true,
      enableMemoryTracking: true
    };

    return enhanced;
  }

  /**
   * Enhance operations object for n8n workflows
   */
  enhanceOperationsForN8n(operations) {
    return {
      ...operations,
      // Add n8n-specific optimizations
      enableBatching: true,
      batchSize: 10,
      enableParallel: false, // Conservative for n8n workflows
      enableCaching: false,  // Disable caching for workflow reliability
      verboseLogging: false  // Reduce noise in n8n logs
    };
  }

  /**
   * Get maximum execution time based on operation type
   */
  getMaxExecutionTime(args) {
    if (!args.inputs || !Array.isArray(args.inputs)) {
      return 30000; // 30 seconds default
    }

    const inputCount = args.inputs.length;
    
    // Base time: 10 seconds + 5 seconds per input
    // Copy operations get extra time
    let baseTime = 10000 + (inputCount * 5000);
    
    if (args.deleteOriginal) {
      baseTime *= 1.5; // 50% more time for copy+delete operations
    }

    // Cap at 10 minutes for n8n workflows
    return Math.min(baseTime, 600000);
  }

  /**
   * Prepare progress callback for n8n workflows
   */
  prepareN8nProgressCallback(sessionId, n8nContext) {
    return (stage, progress) => {
      // Format progress for n8n consumption
      const n8nProgress = {
        type: 'n8n_workflow_progress',
        sessionId,
        workflowId: n8nContext.workflowId,
        executionId: n8nContext.executionId,
        nodeId: n8nContext.nodeId,
        stage,
        progress,
        timestamp: new Date().toISOString()
      };

      // Send to n8n via SSE or stdout depending on context
      this.sendProgressToN8n(n8nProgress);
    };
  }

  /**
   * Send progress updates to n8n
   */
  sendProgressToN8n(progressData) {
    try {
      // If running in n8n context, send via stdout
      if (process.env.N8N_CONTEXT) {
        console.log(JSON.stringify(progressData));
      }
      
      // Log for debugging
      this.logger.debug('n8n progress update:', progressData);
    } catch (error) {
      this.logger.warn('Failed to send progress to n8n:', error);
    }
  }

  /**
   * Handle n8n workflow timeouts
   */
  handleN8nTimeout(toolName, args, executionTime) {
    const maxTime = this.getMaxExecutionTime(args);
    
    if (executionTime > maxTime) {
      const timeoutError = new Error(`n8n workflow timeout: ${toolName} execution exceeded ${maxTime}ms`);
      timeoutError.code = 'N8N_WORKFLOW_TIMEOUT';
      timeoutError.toolName = toolName;
      timeoutError.maxTime = maxTime;
      timeoutError.actualTime = executionTime;
      
      throw timeoutError;
    }
  }

  /**
   * Validate n8n workflow compatibility
   */
  validateN8nCompatibility(toolName, args) {
    // Check for features that might not work well in n8n workflows
    const warnings = [];

    // Large input arrays
    if (args.inputs && args.inputs.length > 20) {
      warnings.push(`Large input array (${args.inputs.length} items) may cause workflow timeouts`);
    }

    // Destructive operations
    if (args.deleteOriginal) {
      warnings.push('Destructive operation (deleteOriginal=true) - ensure proper workflow error handling');
    }

    // Long session IDs
    if (args.sessionId && args.sessionId.length > 50) {
      warnings.push('Long session ID may cause issues in n8n workflow tracking');
    }

    // Return warnings for n8n workflow optimization
    return warnings;
  }

  /**
   * Optimize arguments for n8n workflow performance
   */
  optimizeForN8nWorkflow(toolName, args) {
    const optimized = { ...args };

    // Optimize based on tool type
    switch (toolName) {
      case 'analyze_audit_paths':
        optimized.operations = {
          ...optimized.operations,
          // Reduce operations for faster n8n execution
          analyzeSizes: optimized.operations?.analyzeSizes !== false,
          countFiles: optimized.operations?.countFiles !== false,
          findLongNames: optimized.operations?.findLongNames === true, // Only if explicitly requested
          checkPaths: true // Always check paths
        };
        break;

      case 'copy_audit_folders':
        // Add safety measures for n8n workflows
        optimized.copyOptions = {
          ...optimized.copyOptions,
          verifyIntegrity: true, // Always verify in n8n workflows
          createBackup: optimized.deleteOriginal, // Backup if deleting original
          preserveTimestamps: true
        };
        break;

      case 'generate_long_names_report':
        // Optimize report generation for n8n
        optimized.reportFormat = optimized.reportFormat || 'json'; // JSON is better for n8n
        optimized.maxLength = optimized.maxLength || 250;
        break;
    }

    return optimized;
  }
}
