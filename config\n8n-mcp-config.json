{"name": "audit-path-analyzer", "version": "1.0.0", "description": "Audit file path analyzer with multiple location discovery for n8n workflows", "transport": {"type": "stdio"}, "capabilities": {"tools": true, "resources": true, "prompts": false, "logging": true}, "n8n": {"compatibility": "1.0.0", "category": "file-analysis", "icon": "audit-analyzer.svg", "displayName": "Audit Path Analyzer", "subtitle": "Advanced audit file analysis with multiple location discovery"}, "tools": [{"name": "analyze_audit_paths", "displayName": "Analyze Audit Paths", "description": "Analyze audit file paths with size, count, existence checks, and multiple location discovery", "category": "analysis", "inputSchema": {"type": "object", "properties": {"inputs": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z]{2,8}\\d*[A-Z]*-AUD-(SA|SAL)-\\d{8}$"}, "description": "Array of audit folder names (e.g., AW001-AUD-SA-20250331)", "minItems": 1, "maxItems": 50, "title": "Audit Folder Names"}, "operations": {"type": "object", "properties": {"checkPaths": {"type": "boolean", "default": true, "title": "Check Path Existence", "description": "Verify if audit paths exist in multiple locations"}, "analyzeSizes": {"type": "boolean", "default": true, "title": "Analyze Folder Sizes", "description": "Calculate folder sizes in MB with 2 decimal precision"}, "countFiles": {"type": "boolean", "default": true, "title": "Count <PERSON>", "description": "Count total files and folders recursively"}, "findLongNames": {"type": "boolean", "default": true, "title": "Find Long Names/Paths", "description": "Detect file/folder names or full paths over 250 characters"}}, "title": "Analysis Operations", "description": "Select which operations to perform"}, "sessionId": {"type": "string", "description": "Optional SSE session ID for real-time progress updates in n8n", "title": "Session ID", "pattern": "^[a-zA-Z0-9_-]+$"}, "n8nContext": {"type": "object", "properties": {"workflowId": {"type": "string"}, "executionId": {"type": "string"}, "nodeId": {"type": "string"}}, "description": "n8n workflow context for tracking", "title": "n8n Context"}}, "required": ["inputs"], "additionalProperties": false}, "outputSchema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"input": {"type": "string"}, "auditType": {"type": "string", "enum": ["AUD-SA", "AUD-SAL"]}, "year": {"type": "string"}, "possibleLocations": {"type": "array"}, "foundLocations": {"type": "array"}, "totalSizeInMB": {"type": "number"}, "totalFileCount": {"type": "integer"}, "allLongNamesFiles": {"type": "array"}}}}, "summary": {"type": "object", "properties": {"totalSizeInMB": {"type": "number"}, "totalFiles": {"type": "integer"}, "processedCount": {"type": "integer"}, "successCount": {"type": "integer"}, "errorCount": {"type": "integer"}, "totalLocationsFound": {"type": "integer"}}}, "operationId": {"type": "string"}, "n8nMetadata": {"type": "object", "properties": {"executionTime": {"type": "number"}, "memoryUsage": {"type": "object"}, "warnings": {"type": "array"}}}}}}, {"name": "copy_audit_folders", "displayName": "Copy Audit Folders", "description": "Copy audit folders from discovered source locations to target locations", "category": "file-operations", "inputSchema": {"type": "object", "properties": {"inputs": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z]{2,8}\\d*[A-Z]*-AUD-(SA|SAL)-\\d{8}$"}, "description": "Array of audit folder names to copy", "minItems": 1, "maxItems": 20, "title": "Audit Folder Names"}, "deleteOriginal": {"type": "boolean", "default": false, "title": "Delete Original", "description": "⚠️ WARNING: Delete original folders after successful copy"}, "sessionId": {"type": "string", "description": "Optional SSE session ID for progress updates", "title": "Session ID"}, "copyOptions": {"type": "object", "properties": {"verifyIntegrity": {"type": "boolean", "default": true, "title": "Verify Copy Integrity", "description": "Verify file integrity after copy operation"}, "preserveTimestamps": {"type": "boolean", "default": true, "title": "Preserve Timestamps"}, "createBackup": {"type": "boolean", "default": false, "title": "Create Backup Before Copy"}}, "title": "Copy Options"}}, "required": ["inputs"]}}, {"name": "generate_long_names_report", "displayName": "Generate Long Names Report", "description": "Generate professional table format report of files/folders with long names or paths", "category": "reporting", "inputSchema": {"type": "object", "properties": {"inputs": {"type": "array", "items": {"type": "string"}, "description": "Array of audit folder names to analyze", "title": "Audit Folder Names"}, "maxLength": {"type": "number", "default": 250, "minimum": 50, "maximum": 1000, "title": "Maximum Path/Name Length", "description": "Maximum allowed character length for names and full paths"}, "reportFormat": {"type": "string", "enum": ["table", "csv", "json", "excel"], "default": "table", "title": "Report Format", "description": "Output format for the report"}, "sessionId": {"type": "string", "description": "Optional SSE session ID for progress updates", "title": "Session ID"}}, "required": ["inputs"]}}, {"name": "get_operation_status", "displayName": "Get Operation Status", "description": "Check status of current or specific operations", "category": "monitoring", "inputSchema": {"type": "object", "properties": {"operationId": {"type": "string", "description": "Optional operation ID to check specific operation", "title": "Operation ID"}, "includeDetails": {"type": "boolean", "default": false, "title": "Include Detailed Information", "description": "Include detailed progress and timing information"}}}}], "resources": [{"uri": "analysis_reports", "name": "Analysis Reports", "description": "List of generated analysis reports", "mimeType": "application/json", "n8nDisplayName": "Analysis Reports List"}, {"uri": "long_names_files", "name": "Long Names Files", "description": "Generated long names/paths reports", "mimeType": "application/json", "n8nDisplayName": "Long Names Reports"}, {"uri": "sse_sessions", "name": "SSE Sessions", "description": "Active SSE sessions for progress monitoring", "mimeType": "application/json", "n8nDisplayName": "Progress Sessions"}, {"uri": "health_status", "name": "Health Status", "description": "Server health and performance metrics", "mimeType": "application/json", "n8nDisplayName": "Server Health"}], "environment": {"development": {"logLevel": "debug", "sseHeartbeat": 10000, "maxConcurrentOperations": 5}, "production": {"logLevel": "info", "sseHeartbeat": 30000, "maxConcurrentOperations": 10}, "n8n": {"logLevel": "info", "sseHeartbeat": 15000, "maxConcurrentOperations": 8, "enableMetrics": true, "enableHealthChecks": true}}}