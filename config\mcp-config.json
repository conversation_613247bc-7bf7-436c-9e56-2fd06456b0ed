{"server": {"name": "audit-path-analyzer", "version": "1.0.0", "transport": "stdio", "port": 3001, "host": "localhost"}, "sse": {"endpoint": "/events", "heartbeatInterval": 30000, "connectionTimeout": 1800000, "maxConnections": 100}, "tools": [{"name": "analyze_audit_paths", "description": "Analyze audit file paths with size, count, and existence checks", "inputSchema": {"type": "object", "properties": {"inputs": {"type": "array", "items": {"type": "string"}, "description": "Array of audit folder names to analyze"}, "operations": {"type": "object", "properties": {"checkPaths": {"type": "boolean", "default": true}, "analyzeSizes": {"type": "boolean", "default": true}, "countFiles": {"type": "boolean", "default": true}, "findLongNames": {"type": "boolean", "default": true}, "copyAndMove": {"type": "boolean", "default": false}}}}, "required": ["inputs"]}}, {"name": "copy_audit_folders", "description": "Copy audit folders from source to target location", "inputSchema": {"type": "object", "properties": {"inputs": {"type": "array", "items": {"type": "string"}, "description": "Array of audit folder names to copy"}, "deleteOriginal": {"type": "boolean", "default": false, "description": "Whether to delete original after successful copy"}}, "required": ["inputs"]}}, {"name": "generate_long_names_report", "description": "Generate report of long file/folder names", "inputSchema": {"type": "object", "properties": {"inputs": {"type": "array", "items": {"type": "string"}, "description": "Array of audit folder names to check"}, "maxLength": {"type": "number", "default": 250, "description": "Maximum allowed name length"}}, "required": ["inputs"]}}], "resources": [{"uri": "analysis_reports/{reportId}", "name": "Analysis Report", "description": "Generated analysis reports", "mimeType": "application/json"}, {"uri": "progress_events/{sessionId}", "name": "Progress Events", "description": "SSE progress event streams", "mimeType": "text/event-stream"}, {"uri": "long_names_files/{filename}", "name": "Long Names File", "description": "Long name detection files", "mimeType": "text/plain"}]}