# Audit Path Analyzer MCP Server

A comprehensive Model Context Protocol (MCP) server with Server-Sent Events (SSE) functionality for analyzing and managing audit department file paths and operations.

## Features

- ✅ **Multiple Location Discovery**: Automatically finds audit folders across multiple possible locations
- ✅ **Path Analysis**: Analyze audit folders for existence, size, and file count across all found locations
- ✅ **Long Name Detection**: Find files/folders with names over 250 characters in all locations
- ✅ **File Operations**: Copy audit folders from any discovered source to target locations
- ✅ **Real-time Progress**: SSE-based progress updates for long operations
- ✅ **MCP Protocol**: Full Model Context Protocol compliance
- ✅ **CLI Interface**: Command-line interface for easy interaction with location visualization
- ✅ **Comprehensive Logging**: Detailed logging with configurable levels

## Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- npm or yarn package manager
- Access to audit file directories

### Installation
```bash
# Clone the project
git clone <repository-url>
cd audit-path-analyzer

# Install dependencies
npm install

# Create output directories
mkdir -p output/reports output/long-names
```

### Basic Usage
```bash
# Start the MCP server
npm start

# Or use the interactive CLI
npm run client

# For n8n integration
npm run n8n:start
```

## n8n Integration

This MCP server provides comprehensive n8n workflow integration for audit file management.

### n8n Setup
```bash
# Start n8n MCP server
npm run n8n:start

# Development mode with auto-reload
npm run n8n:dev

# Validate n8n configuration
npm run n8n:validate
```

### n8n Server Registration
1. Copy `n8n-manifest.json` to your n8n configuration directory
2. Register the server in n8n settings:
```json
{
  "mcp": {
    "servers": {
      "audit-path-analyzer": {
        "command": "node",
        "args": ["src/n8n/n8n-server.js"],
        "cwd": "/path/to/audit-path-analyzer",
        "env": {
          "NODE_ENV": "production",
          "N8N_CONTEXT": "true"
        }
      }
    }
  }
}
```

### Available n8n Nodes
- **Analyze Audit Paths**: Multi-location analysis with comprehensive reporting
- **Copy Audit Folders**: Safe copy operations with integrity verification
- **Generate Long Names Report**: Professional table format compliance reports
- **Get Operation Status**: Real-time operation monitoring

### n8n Features
- **Workflow-Optimized**: Enhanced error handling and retry strategies for n8n
- **Real-time Progress**: SSE integration for workflow progress tracking
- **Professional Formatting**: n8n-friendly data structures and error messages
- **Comprehensive Validation**: Input validation with detailed error guidance

See [n8n Integration Guide](docs/n8n-integration-guide.md) for complete setup instructions and workflow examples.

## Path Resolution Logic

The system automatically resolves audit folder paths based on naming conventions:

### AUD-SA Folders
Example: `AW001-AUD-***********`
- **Source**: `H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********`
- **Target**: `H:\FILES\2_AUDIT DEPT\Lock 2025\AC\W\AW001-AUD-***********`

### AUD-SAL Folders  
Example: `ES0083JL-AUD-SAL-20241231`
- **Source**: `H:\FILES\2_AUDIT DEPT\Year 2024\Listed\E\ES0083JL`
- **Target**: `H:\FILES\2_AUDIT DEPT\Lock 2024\Listed\E\ES0083JL`

## Available MCP Tools

### 1. analyze_audit_paths
Analyze audit folders for existence, size, file count, and long names.

```json
{
  "inputs": ["AW001-AUD-***********", "ES0083JL-AUD-SAL-20241231"],
  "operations": {
    "checkPaths": true,
    "analyzeSizes": true,
    "countFiles": true,
    "findLongNames": true
  },
  "sessionId": "optional-session-id"
}
```

### 2. copy_audit_folders
Copy audit folders from source to target locations.

```json
{
  "inputs": ["AW001-AUD-***********"],
  "deleteOriginal": false,
  "sessionId": "optional-session-id"
}
```

### 3. generate_long_names_report
Generate detailed report of files/folders with long names.

```json
{
  "inputs": ["AW001-AUD-***********"],
  "maxLength": 250,
  "sessionId": "optional-session-id"
}
```

### 4. get_operation_status
Check status of current or specific operations.

```json
{
  "operationId": "optional-operation-id"
}
```

## Server-Sent Events (SSE)

Connect to the SSE endpoint for real-time progress updates:

```
GET http://localhost:3001/events/{sessionId}
```

### Event Types
- `start`: Operation started
- `progress`: Progress update
- `complete`: Operation completed
- `error`: Error occurred
- `heartbeat`: Keep-alive heartbeat

## CLI Commands

### Interactive Mode
```bash
npm run client
```

### Direct Commands
```bash
# Analyze with sample data
node src/client/cli-interface.js analyze --sample

# Copy folders
node src/client/cli-interface.js copy --sample

# Generate long names report
node src/client/cli-interface.js long-names --sample

# Test path resolution
node src/client/cli-interface.js test-paths
```

### Advanced Options
```bash
# Analyze specific folders
node src/client/cli-interface.js analyze -i "AW001-AUD-***********,AY0138-AUD-SA-20241231"

# Copy and delete originals (dangerous!)
node src/client/cli-interface.js copy --sample --delete-original

# Custom long name threshold
node src/client/cli-interface.js long-names --sample --max-length 200
```

## Configuration

### Path Mappings (`config/path-mappings.json`)
Configure source/target paths and character mappings:

```json
{
  "basePaths": {
    "source": {
      "AUD-SA": "H:\\FILES\\2_AUDIT DEPT\\Year {year}",
      "AUD-SAL": "H:\\FILES\\2_AUDIT DEPT\\Year {year}\\Listed"
    },
    "target": {
      "AUD-SA": "H:\\FILES\\2_AUDIT DEPT\\Lock {year}",
      "AUD-SAL": "H:\\FILES\\2_AUDIT DEPT\\Lock {year}\\Listed"
    }
  },
  "characterMappings": {
    "A": "AC", "E": "E", "F": "FC", "G": "GC",
    "Y": "IY", "J": "JC", "M": "MC", "P": "PW",
    "Q": "Q", "R": "RL", "S": "SH", "T": "TO", "Z": "Z"
  }
}
```

### Server Configuration (`config/mcp-config.json`)
Configure server settings, SSE options, and tool definitions.

## Health Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Statistics
```bash
curl http://localhost:3001/stats
```

## Development

### Running Tests
```bash
# Unit tests
npm test

# Test coverage
npm run test:coverage

# Integration tests  
npm run test:integration

# Watch mode
npm run test:watch
```

### Development Mode
```bash
# Auto-reload on changes
npm run dev

# Debug mode
LOG_LEVEL=debug npm start
```

## Project Structure

```
audit-path-analyzer/
├── package.json                     # Dependencies and scripts
├── config/                          # Configuration files
│   ├── mcp-config.json             # MCP server configuration
│   └── path-mappings.json          # Path mapping rules
├── src/
│   ├── server/                     # MCP Server components
│   │   ├── index.js               # Main entry point
│   │   ├── mcp-server.js          # MCP protocol handler
│   │   ├── sse-manager.js         # SSE event manager
│   │   ├── path-analyzer.js       # Path analysis logic
│   │   └── file-operations.js     # File operations
│   ├── client/                     # Client components
│   │   └── cli-interface.js       # CLI interface
│   ├── shared/                     # Shared utilities
│   │   ├── constants.js           # Application constants
│   │   ├── path-utils.js          # Path utilities
│   │   └── logger.js              # Logging utility
│   └── tests/                      # Test files
│       ├── unit/                  # Unit tests
│       └── integration/           # Integration tests
├── docs/                           # Documentation
│   ├── operation-guide.md         # Operation guide
│   └── test-plan.md              # Test plan
└── output/                         # Generated files
    ├── reports/                   # Analysis reports
    └── long-names/               # Long name reports
```

## Example Output

### Analysis Result with Multiple Locations
```json
{
  "results": [
    {
      "input": "AW001-AUD-***********",
      "auditType": "AUD-SA",
      "year": "2025",
      "possibleLocations": [
        {
          "type": "primary",
          "description": "Primary location based on naming convention"
        },
        {
          "type": "archived",
          "description": "Archived location"
        },
        {
          "type": "completed",
          "description": "Completed location"
        }
      ],
      "foundLocations": [
        {
          "type": "primary",
          "sourcePath": "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********",
          "targetPath": "H:\\FILES\\2_AUDIT DEPT\\Lock 2025\\AC\\W\\AW001-AUD-***********",
          "exists": true,
          "sizeInMB": 1234.56,
          "fileCount": 150,
          "lastModified": "2025-01-07T09:00:00.000Z",
          "permissions": {
            "readable": true,
            "writable": true
          },
          "longNamesFile": "AW001-AUD-***********-primary-20250107.txt"
        },
        {
          "type": "archived",
          "sourcePath": "H:\\FILES\\2_AUDIT DEPT\\Archive 2025\\AC\\W\\AW001-AUD-***********",
          "targetPath": "H:\\FILES\\2_AUDIT DEPT\\Archive_Lock 2025\\AC\\W\\AW001-AUD-***********",
          "exists": true,
          "sizeInMB": 856.34,
          "fileCount": 89,
          "lastModified": "2024-12-15T14:30:00.000Z",
          "permissions": {
            "readable": true,
            "writable": false
          },
          "longNamesFile": null
        }
      ],
      "totalSizeInMB": 2090.90,
      "totalFileCount": 239,
      "allLongNamesFiles": ["AW001-AUD-***********-primary-20250107.txt"]
    }
  ],
  "summary": {
    "totalSizeInMB": 2090.90,
    "totalFiles": 239,
    "processedCount": 1,
    "successCount": 1,
    "errorCount": 0,
    "totalLocationsFound": 2
  }
}
```


FILES (2 items):
----------------------------------------
1. very-long-filename-that-exceeds-the-250-character-limit-and-should-be-reported-in-this-analysis-because-it-might-cause-issues-with-file-systems-that-have-path-length-limitations-especially-on-windows-systems.xlsx (267 chars)
   Path: documents\very-long-filename-that-exceeds-the-250-character-limit...
   Full: H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\documents\very-long-filename...

```
### Long Names/Paths Report (Table Format)
```
Long Names/Paths Report
Generated: 2025-01-07T09:00:00.000Z
Scanned Path: H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********
Length Threshold: 250 characters
Total Items Found: 3


| No. of Characters | Full Path                                                                     | Type   | Violation |
|-------------------|-------------------------------------------------------------------------------|--------|-----------|
|               287 | H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\documents\ve... | file   | path      |
|               265 | H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\reports\fold... | folder | both      |
|               254 | H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\very-long-fi... | file   | name      |
|-------------------|-------------------------------------------------------------------------------|--------|-----------|

SUMMARY:
----------------------------------------
Total Items: 3
- Files: 2
- Folders: 1

Violation Types:
- Name too long: 1
- Path too long: 2
- Both name and path: 1

Longest Path: 287 characters
Path: H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\documents\very-long-filename.xlsx

CSV FORMAT (for spreadsheet import):
----------------------------------------
"Character Count","Full Path","Type","Violation Type"
"287","H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\documents\very-long-filename.xlsx","file","path"
"265","H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\reports\folder-name","folder","both"
"254","H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\very-long-filename.doc","file","name"
```
================================================================================

FILES (2 items):
----------------------------------------
1. very-long-filename-that-exceeds-the-250-character-limit-and-should-be-reported-in-this-analysis-because-it-might-cause-issues-with-file-systems-that-have-path-length-limitations-especially-on-windows-systems.xlsx (267 chars)
   Path: documents\very-long-filename-that-exceeds-the-250-character-limit...
   Full: H:\FILES\2_AUDIT DEPT\Year 2025\AC\W\AW001-AUD-***********\documents\very-long-filename...

```

## Performance

- **Analysis**: Handles folders with 10,000+ files efficiently
- **SSE Connections**: Supports up to 100 concurrent connections
- **Memory Usage**: < 500MB for typical workloads
- **Concurrent Operations**: Supports multiple simultaneous operations

## Security

- **Input Validation**: All inputs validated against expected patterns
- **Path Traversal Protection**: Path resolution restricted to configured base paths
- **File Permissions**: Proper permission checks before operations
- **Network Security**: Configurable access controls for SSE endpoints

## Troubleshooting

### Common Issues

1. **"Path not found" errors**
   - Verify network access to H: drive
   - Check path mapping configuration
   - Ensure audit folder names follow correct format

2. **SSE connection timeouts**
   - Check firewall settings for port 3001
   - Verify sessionId is unique and valid

3. **Permission errors**
   - Verify read/write permissions to audit directories
   - Check available disk space

### Debug Mode
```bash
LOG_LEVEL=debug npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues or questions:
1. Check the operation guide in `docs/operation-guide.md`
2. Review the test plan in `docs/test-plan.md`
3. Test with sample data using the CLI
4. Check server logs for error details

---

**Built with ❤️ for the Audit Department**
