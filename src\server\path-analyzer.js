/**
 * Core path analysis functionality for audit folders
 */

import fs from 'fs-extra';
import path from 'path';
import { 
  getPathComponents, 
  isNameTooLong, 
  normalizePath 
} from '../shared/path-utils.js';
import { 
  ERROR_CODES, 
  PROGRESS_STAGES, 
  DEFAULT_CONFIG 
} from '../shared/constants.js';

export class PathAnalyzer {
  constructor(sseManager, logger) {
    this.sseManager = sseManager;
    this.logger = logger;
    this.currentOperations = new Map();
  }

  /**
   * Analyze multiple audit paths
   * @param {string[]} inputs - Array of audit folder names
   * @param {object} operations - Operations to perform
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Analysis results
   */
  async analyzeAuditPaths(inputs, operations = {}, sessionId = null) {
    const operationId = `analysis_${Date.now()}`;
    this.currentOperations.set(operationId, { inputs, sessionId, startTime: Date.now() });

    try {
      this.logger.info(`Starting analysis for ${inputs.length} inputs`, { operationId, inputs });
      
      // Emit start event
      if (sessionId) {
        this.sseManager.broadcast(sessionId, {
          type: 'start',
          data: { operationId, totalInputs: inputs.length, operations }
        });
      }

      // Process each input
      const results = [];
      let totalSizeInMB = 0;
      let totalFiles = 0;

      for (let i = 0; i < inputs.length; i++) {
        const input = inputs[i];
        this.logger.info(`Processing input ${i + 1}/${inputs.length}: ${input}`);

        // Emit progress
        if (sessionId) {
          this.sseManager.broadcast(sessionId, {
            type: 'progress',
            data: {
              stage: PROGRESS_STAGES.ANALYZING_PATHS,
              current: i + 1,
              total: inputs.length,
              currentInput: input
            }
          });
        }

        try {
          const result = await this.analyzeSinglePath(input, operations, sessionId);
          results.push(result);
          
          if (result.sizeInMB) totalSizeInMB += result.sizeInMB;
          if (result.fileCount) totalFiles += result.fileCount;

        } catch (error) {
          this.logger.error(`Failed to analyze ${input}:`, error);
          results.push({
            input,
            error: error.message,
            exists: false
          });
        }
      }

      const summary = {
        totalSizeInMB: Math.round(totalSizeInMB * 100) / 100,
        totalFiles,
        processedCount: results.length,
        successCount: results.filter(r => !r.error).length,
        errorCount: results.filter(r => r.error).length
      };

      const finalResult = { results, summary, operationId };

      // Emit completion
      if (sessionId) {
        this.sseManager.broadcast(sessionId, {
          type: 'complete',
          data: finalResult
        });
      }

      this.currentOperations.delete(operationId);
      this.logger.info(`Analysis completed`, { operationId, summary });

      return finalResult;

    } catch (error) {
      this.logger.error(`Analysis failed:`, error);
      
      if (sessionId) {
        this.sseManager.broadcast(sessionId, {
          type: 'error',
          data: { error: error.message, operationId }
        });
      }

      this.currentOperations.delete(operationId);
      throw error;
    }
  }

  /**
   * Analyze a single audit path with multiple location support
   * @param {string} input - Audit folder name
   * @param {object} operations - Operations to perform
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Single path analysis result with multiple locations
   */
  async analyzeSinglePath(input, operations, sessionId) {
    const pathComponents = getPathComponents(input);
    
    if (!pathComponents.valid) {
      throw new Error(pathComponents.error);
    }

    // Find all possible locations for this input
    const possibleLocations = await this.findAllPossibleLocations(input, pathComponents);

    const result = {
      input: pathComponents.input,
      auditType: pathComponents.auditType,
      year: pathComponents.year,
      possibleLocations,
      foundLocations: [],
      totalSizeInMB: 0,
      totalFileCount: 0,
      allLongNamesFiles: []
    };

    // Check each possible location
    for (const location of possibleLocations) {
      const locationResult = await this.analyzeLocation(location, operations, sessionId);
      if (locationResult.exists) {
        result.foundLocations.push(locationResult);
        result.totalSizeInMB += locationResult.sizeInMB || 0;
        result.totalFileCount += locationResult.fileCount || 0;
        if (locationResult.longNamesFile) {
          result.allLongNamesFiles.push(locationResult.longNamesFile);
        }
      }
    }

    // Round total size
    result.totalSizeInMB = Math.round(result.totalSizeInMB * 100) / 100;

    return result;
  }

  /**
   * Find all possible locations for an audit folder
   * @param {string} input - Audit folder name
   * @param {object} pathComponents - Base path components
   * @returns {Promise<Array>} - Array of possible location objects
   */
  async findAllPossibleLocations(input, pathComponents) {
    const locations = [];
    
    // Add primary location
    locations.push({
      type: 'primary',
      sourcePath: pathComponents.sourcePath,
      targetPath: pathComponents.targetPath,
      description: 'Primary location based on naming convention'
    });

    // For AUD-SA, also check alternative character mappings or year variations
    if (pathComponents.auditType === 'AUD-SA') {
      // Check alternative years (current year, previous year, next year)
      const baseYear = parseInt(pathComponents.year);
      const alternativeYears = [baseYear - 1, baseYear + 1];
      
      for (const altYear of alternativeYears) {
        try {
          const altInput = input.replace(/\d{4}/, altYear.toString());
          const altComponents = getPathComponents(altInput);
          if (altComponents.valid) {
            locations.push({
              type: 'alternative_year',
              sourcePath: altComponents.sourcePath,
              targetPath: altComponents.targetPath,
              description: `Alternative year: ${altYear}`,
              year: altYear.toString()
            });
          }
        } catch (error) {
          // Ignore invalid alternative paths
        }
      }

      // Check if folder exists directly in base directory (without character mapping)
      const directPath = pathComponents.sourcePath.replace(/\\[^\\]+\\[^\\]+\\([^\\]+)$/, '\\$1');
      locations.push({
        type: 'direct',
        sourcePath: directPath,
        targetPath: directPath.replace('Year', 'Lock'),
        description: 'Direct path without character mapping'
      });
    }

    // For AUD-SAL, check if folder exists in non-Listed directory
    if (pathComponents.auditType === 'AUD-SAL') {
      const nonListedPath = pathComponents.sourcePath.replace('\\Listed\\', '\\');
      locations.push({
        type: 'non_listed',
        sourcePath: nonListedPath,
        targetPath: nonListedPath.replace('Year', 'Lock'),
        description: 'Non-Listed directory path'
      });
    }

    // Check archived locations (Archive folder)
    const archivedPath = pathComponents.sourcePath.replace('Year', 'Archive');
    locations.push({
      type: 'archived',
      sourcePath: archivedPath,
      targetPath: archivedPath.replace('Archive', 'Lock'),
      description: 'Archived location'
    });

    // Check completed locations (Completed folder)
    const completedPath = pathComponents.sourcePath.replace('Year', 'Completed');
    locations.push({
      type: 'completed',
      sourcePath: completedPath,
      targetPath: completedPath.replace('Completed', 'Lock'),
      description: 'Completed location'
    });

    return locations;
  }

  /**
   * Analyze a specific location
   * @param {object} location - Location object to analyze
   * @param {object} operations - Operations to perform
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Location analysis result
   */
  async analyzeLocation(location, operations, sessionId) {
    const result = {
      ...location,
      exists: false,
      sizeInMB: 0,
      fileCount: 0,
      folderCount: 0,
      longNamesFile: null,
      lastModified: null,
      fullPathsList: [],
      permissions: {
        readable: false,
        writable: false
      }
    };

    // Check if path exists
    if (operations.checkPaths !== false) {
      result.exists = await this.checkPathExists(location.sourcePath);
      
      if (!result.exists) {
        return result;
      }

      // Get all full paths under this location
      result.fullPathsList = await this.getAllFullPaths(location.sourcePath);

      // Get additional path information
      try {
        const stats = await fs.stat(location.sourcePath);
        result.lastModified = stats.mtime.toISOString();
        
        // Check permissions
        try {
          await fs.access(location.sourcePath, fs.constants.R_OK);
          result.permissions.readable = true;
        } catch (error) {
          // No read permission
        }
        
        try {
          await fs.access(location.sourcePath, fs.constants.W_OK);
          result.permissions.writable = true;
        } catch (error) {
          // No write permission
        }
      } catch (error) {
        this.logger.warn(`Error getting stats for ${location.sourcePath}:`, error.message);
      }
    }

    // Analyze folder size and file count
    if (operations.analyzeSizes !== false || operations.countFiles !== false) {
      try {
        const folderStats = await this.analyzeFolderStats(location.sourcePath, sessionId);
        
        if (operations.analyzeSizes !== false) {
          result.sizeInMB = folderStats.sizeInMB;
          result.totalSizeInBytes = folderStats.totalSizeInMB;
        }
        
        if (operations.countFiles !== false) {
          result.fileCount = folderStats.fileCount;
          result.folderCount = folderStats.folderCount;
        }
      } catch (error) {
        this.logger.warn(`Error analyzing folder stats for ${location.sourcePath}:`, error.message);
      }
    }

    // Check for long names
    if (operations.findLongNames !== false && result.exists) {
      try {
        const longNamesFile = `${path.basename(location.sourcePath)}-${location.type}-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.txt`;
        result.longNamesFile = await this.checkLongNames(location.sourcePath, longNamesFile, sessionId);
      } catch (error) {
        this.logger.warn(`Error checking long names for ${location.sourcePath}:`, error.message);
      }
    }

    return result;
  }

  /**
   * Get all full paths recursively under a directory
   * @param {string} dirPath - Directory path to scan
   * @returns {Promise<string[]>} - Array of all full paths
   */
  async getAllFullPaths(dirPath) {
    const allPaths = [];

    const scanDirectory = async (currentPath) => {
      try {
        const items = await fs.readdir(currentPath);
        
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          const normalizedPath = normalizePath(itemPath);
          
          // Add this path to the list
          allPaths.push(normalizedPath);
          
          // If it's a directory, recurse into it
          const stats = await fs.stat(itemPath);
          if (stats.isDirectory()) {
            await scanDirectory(itemPath);
          }
        }
      } catch (error) {
        this.logger.warn(`Error scanning directory ${currentPath}:`, error.message);
      }
    };

    await scanDirectory(dirPath);
    
    // Sort paths for consistent output
    return allPaths.sort();
  }

  /**
   * Check if a path exists
   * @param {string} pathToCheck - Path to verify
   * @returns {Promise<boolean>} - True if path exists
   */
  async checkPathExists(pathToCheck) {
    try {
      const stats = await fs.stat(pathToCheck);
      return stats.isDirectory();
    } catch (error) {
      if (error.code === 'ENOENT') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Analyze folder statistics (size and file count)
   * @param {string} folderPath - Path to analyze
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<object>} - Folder statistics
   */
  async analyzeFolderStats(folderPath, sessionId) {
    let totalSize = 0;
    let fileCount = 0;
    let folderCount = 0;

    const processDirectory = async (dirPath) => {
      try {
        const items = await fs.readdir(dirPath);
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const stats = await fs.stat(itemPath);
          
          if (stats.isFile()) {
            totalSize += stats.size;
            fileCount++;
          } else if (stats.isDirectory()) {
            folderCount++;
            await processDirectory(itemPath); // Recursive
          }
        }
      } catch (error) {
        this.logger.warn(`Error processing directory ${dirPath}:`, error.message);
      }
    };

    await processDirectory(folderPath);

    const sizeInMB = Math.round((totalSize / (1024 * 1024)) * 100) / 100;

    return {
      totalSizeInMB: totalSize,
      sizeInMB,
      fileCount,
      folderCount
    };
  }

  /**
   * Check for long file/folder names and full paths exceeding threshold and generate report
   * @param {string} folderPath - Path to scan
   * @param {string} reportFilename - Output filename
   * @param {string} sessionId - SSE session ID
   * @returns {Promise<string|null>} - Report filename if long names/paths found
   */
  async checkLongNames(folderPath, reportFilename, sessionId) {
    const longItems = [];
    const threshold = DEFAULT_CONFIG.LONG_NAME_THRESHOLD;

    const scanDirectory = async (dirPath, relativePath = '') => {
      try {
        const items = await fs.readdir(dirPath);
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const fullRelativePath = relativePath ? path.join(relativePath, item) : item;
          const fullPath = normalizePath(itemPath);
          
          // Check if name is too long OR full path is too long
          const nameLength = item.length;
          const pathLength = fullPath.length;
          const isNameTooLong = nameLength > threshold;
          const isPathTooLong = pathLength > threshold;
          
          if (isNameTooLong || isPathTooLong) {
            const stats = await fs.stat(itemPath);
            longItems.push({
              name: item,
              path: fullRelativePath,
              fullPath: fullPath,
              type: stats.isDirectory() ? 'folder' : 'file',
              nameLength: nameLength,
              pathLength: pathLength,
              isNameTooLong: isNameTooLong,
              isPathTooLong: isPathTooLong,
              violationType: isNameTooLong && isPathTooLong ? 'both' : 
                           isNameTooLong ? 'name' : 'path'
            });
          }
          
          // Recurse into directories
          const stats = await fs.stat(itemPath);
          if (stats.isDirectory()) {
            await scanDirectory(itemPath, fullRelativePath);
          }
        }
      } catch (error) {
        this.logger.warn(`Error scanning directory ${dirPath}:`, error.message);
      }
    };

    // Emit progress for long names checking
    if (sessionId) {
      this.sseManager.broadcast(sessionId, {
        type: 'progress',
        data: {
          stage: PROGRESS_STAGES.CHECKING_LONG_NAMES,
          message: `Scanning for names and paths over ${threshold} characters`
        }
      });
    }

    await scanDirectory(folderPath);

    if (longItems.length > 0) {
      // Generate report file
      const outputDir = './output/long-names';
      await fs.ensureDir(outputDir);
      
      const reportPath = path.join(outputDir, reportFilename);
      const reportContent = this.generateLongNamesReport(longItems, folderPath, threshold);
      
      await fs.writeFile(reportPath, reportContent, 'utf8');
      
      this.logger.info(`Generated long names/paths report: ${reportPath} (${longItems.length} items)`);
      return reportFilename;
    }

    return null;
  }

  /**
   * Generate long names/paths report content in table format
   * @param {object[]} longItems - Array of long name/path items
   * @param {string} folderPath - Scanned folder path
   * @param {number} threshold - Length threshold
   * @returns {string} - Report content in table format
   */
  generateLongNamesReport(longItems, folderPath, threshold) {
    const timestamp = new Date().toISOString();
    
    let content = `Long Names/Paths Report\n`;
    content += `Generated: ${timestamp}\n`;
    content += `Scanned Path: ${folderPath}\n`;
    content += `Length Threshold: ${threshold} characters\n`;
    content += `Total Items Found: ${longItems.length}\n`;
    content += `\n${'='.repeat(120)}\n\n`;

    // Create table header
    content += `| No. of Characters | Full Path${' '.repeat(85)} | Type     | Violation |\n`;
    content += `|${'-'.repeat(18)}|${'-'.repeat(95)}|${'-'.repeat(9)}|${'-'.repeat(10)}|\n`;

    // Sort items by path length (longest first)
    const sortedItems = [...longItems].sort((a, b) => b.pathLength - a.pathLength);

    sortedItems.forEach((item, index) => {
      const charCount = item.pathLength.toString().padStart(17);
      const pathDisplay = item.fullPath.length > 93 ? 
        item.fullPath.substring(0, 90) + '...' : 
        item.fullPath.padEnd(93);
      const typeDisplay = item.type.padEnd(7);
      const violationDisplay = item.violationType.padEnd(8);
      
      content += `|${charCount} | ${pathDisplay} | ${typeDisplay} | ${violationDisplay} |\n`;
    });

    content += `|${'-'.repeat(18)}|${'-'.repeat(95)}|${'-'.repeat(9)}|${'-'.repeat(10)}|\n`;

    // Summary section
    content += `\nSUMMARY:\n`;
    content += `${'-'.repeat(40)}\n`;
    const nameViolations = longItems.filter(item => item.isNameTooLong).length;
    const pathViolations = longItems.filter(item => item.isPathTooLong).length;
    const bothViolations = longItems.filter(item => item.violationType === 'both').length;
    const folders = longItems.filter(item => item.type === 'folder').length;
    const files = longItems.filter(item => item.type === 'file').length;
    
    content += `Total Items: ${longItems.length}\n`;
    content += `- Files: ${files}\n`;
    content += `- Folders: ${folders}\n\n`;
    content += `Violation Types:\n`;
    content += `- Name too long: ${nameViolations}\n`;
    content += `- Path too long: ${pathViolations}\n`;
    content += `- Both name and path: ${bothViolations}\n\n`;
    
    // Add longest path info
    if (sortedItems.length > 0) {
      const longestItem = sortedItems[0];
      content += `Longest Path: ${longestItem.pathLength} characters\n`;
      content += `Path: ${longestItem.fullPath}\n\n`;
    }

    // CSV format section for easy import
    content += `CSV FORMAT (for spreadsheet import):\n`;
    content += `${'-'.repeat(40)}\n`;
    content += `"Character Count","Full Path","Type","Violation Type"\n`;
    sortedItems.forEach(item => {
      content += `"${item.pathLength}","${item.fullPath.replace(/"/g, '""')}","${item.type}","${item.violationType}"\n`;
    });

    return content;
  }

  /**
   * Get current operation status
   * @param {string} operationId - Operation ID to check
   * @returns {object|null} - Operation status or null if not found
   */
  getOperationStatus(operationId) {
    const operation = this.currentOperations.get(operationId);
    if (!operation) return null;

    return {
      operationId,
      inputs: operation.inputs,
      sessionId: operation.sessionId,
      startTime: operation.startTime,
      duration: Date.now() - operation.startTime
    };
  }

  /**
   * Get all current operations
   * @returns {object[]} - Array of current operations
   */
  getCurrentOperations() {
    return Array.from(this.currentOperations.entries()).map(([id, op]) => ({
      operationId: id,
      ...this.getOperationStatus(id)
    }));
  }
}
