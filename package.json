{"name": "audit-path-analyzer", "version": "1.0.0", "description": "MCP server with SSE for audit file path analysis and operations", "main": "src/server/index.js", "type": "module", "scripts": {"start": "node src/server/index.js", "dev": "node --watch src/server/index.js", "test": "node scripts/run-tests.js", "build": "node scripts/build.js", "client": "node src/client/cli-interface.js", "n8n:start": "node src/n8n/n8n-server.js", "n8n:dev": "NODE_ENV=development node --watch src/n8n/n8n-server.js", "n8n:production": "NODE_ENV=production node src/n8n/n8n-server.js", "n8n:test": "N8N_CONTEXT=true npm run test", "n8n:validate": "node scripts/validate-n8n-config.js"}, "keywords": ["mcp", "server-sent-events", "audit", "file-analysis", "path-analyzer"], "author": "Audit Department", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "express": "^4.18.2", "cors": "^2.8.5", "fs-extra": "^11.2.0", "path": "^0.12.7", "chalk": "^5.3.0", "commander": "^11.1.0", "inquirer": "^9.2.12", "progress": "^2.0.3"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}