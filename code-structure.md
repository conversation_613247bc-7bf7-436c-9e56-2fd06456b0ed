# Audit File Path Analyzer - Code Structure

## Project Directory Structure

```
audit-path-analyzer/
├── package.json                     # Project dependencies and scripts
├── package-lock.json               # Dependency lock file
├── README.md                       # Project documentation
├── .gitignore                      # Git ignore file
├── config/
│   ├── mcp-config.json            # MCP server configuration
│   └── path-mappings.json         # Path mapping configurations
├── src/
│   ├── server/                    # MCP Server components
│   │   ├── index.js              # Main MCP server entry point
│   │   ├── mcp-server.js         # MCP protocol handler
│   │   ├── sse-manager.js        # Server-Sent Events manager
│   │   ├── path-analyzer.js      # Core path analysis logic
│   │   ├── file-operations.js    # File copy/move/delete operations
│   │   └── report-generator.js   # Report and file generation
│   ├── client/                    # Client components
│   │   ├── mcp-client.js         # MCP client connection
│   │   ├── sse-client.js         # SSE event handling
│   │   ├── cli-interface.js      # Command line interface
│   │   └── progress-display.js   # Progress visualization
│   ├── shared/                    # Shared utilities
│   │   ├── path-utils.js         # Path manipulation utilities
│   │   ├── validation.js         # Input validation
│   │   ├── constants.js          # Application constants
│   │   └── logger.js             # Logging utilities
│   └── tests/                     # Test files
│       ├── unit/                 # Unit tests
│       │   ├── path-analyzer.test.js
│       │   ├── file-operations.test.js
│       │   └── path-utils.test.js
│       ├── integration/          # Integration tests
│       │   ├── mcp-server.test.js
│       │   └── end-to-end.test.js
│       └── fixtures/             # Test data and mock files
│           ├── sample-inputs.json
│           └── mock-directories/
├── scripts/                       # Build and utility scripts
│   ├── build.js                  # Build script
│   ├── start-server.js           # Server startup script
│   └── run-tests.js              # Test runner script
├── docs/                          # Documentation
│   ├── api-reference.md          # API documentation
│   ├── user-guide.md             # User guide
│   └── deployment.md             # Deployment instructions
└── output/                        # Generated output files
    ├── reports/                  # Analysis reports
    └── long-names/               # Long name detection files
```

## Key Components Architecture

### 1. MCP Server Core (`src/server/mcp-server.js`)
- Implements MCP protocol communication
- Registers tools and resources
- Handles client connections
- Routes requests to appropriate handlers

### 2. SSE Manager (`src/server/sse-manager.js`)
- Manages Server-Sent Events connections
- Broadcasts progress updates
- Handles connection lifecycle
- Event queuing and buffering

### 3. Path Analyzer (`src/server/path-analyzer.js`)
- Core business logic for path analysis
- Implements path mapping rules
- Handles recursive directory scanning
- Size and file count calculations

### 4. File Operations (`src/server/file-operations.js`)
- File system operations (copy, move, delete)
- Directory creation and management
- Permission and space validation
- Progress tracking for large operations

### 5. Report Generator (`src/server/report-generator.js`)
- Generates analysis reports
- Creates long name detection files
- Formats output data
- File naming with timestamps

### 6. Path Utils (`src/shared/path-utils.js`)
- Path resolution and manipulation
- Character mapping logic
- Year extraction and path building
- Cross-platform path handling

## Data Flow Architecture

```
Client Request → MCP Server → Path Analyzer → File Operations
                     ↓              ↓              ↓
                SSE Manager ← Progress Updates ← Report Generator
                     ↓
                Client SSE ← Real-time Updates
```

## Configuration Management

### MCP Configuration (`config/mcp-config.json`)
```json
{
  "server": {
    "name": "audit-path-analyzer",
    "version": "1.0.0",
    "transport": "stdio"
  },
  "tools": [
    "analyze_audit_paths",
    "copy_audit_folders",
    "generate_long_names_report"
  ],
  "resources": [
    "analysis_reports",
    "progress_events"
  ]
}
```

### Path Mappings (`config/path-mappings.json`)
```json
{
  "basePaths": {
    "source": {
      "AUD-SA": "H:\\FILES\\2_AUDIT DEPT\\Year {year}",
      "AUD-SAL": "H:\\FILES\\2_AUDIT DEPT\\Year {year}\\Listed"
    },
    "target": {
      "AUD-SA": "H:\\FILES\\2_AUDIT DEPT\\Lock {year}",
      "AUD-SAL": "H:\\FILES\\2_AUDIT DEPT\\Lock {year}\\Listed"
    }
  },
  "characterMappings": {
    "A": "AC", "E": "E", "F": "FC", "G": "GC",
    "Y": "IY", "J": "JC", "M": "MC", "P": "PW",
    "Q": "Q", "R": "RL", "S": "SH", "T": "TO", "Z": "Z"
  }
}
```

## API Interface Design

### MCP Tools
1. `analyze_audit_paths` - Main analysis tool
2. `copy_audit_folders` - Copy/move operations
3. `generate_long_names_report` - Long name detection
4. `get_analysis_progress` - Progress tracking

### MCP Resources
1. `analysis_reports/{reportId}` - Generated reports
2. `progress_events/{sessionId}` - SSE event streams
3. `long_names_files/{filename}` - Long name detection files

## Error Handling Strategy

### Error Categories
1. **Validation Errors**: Invalid input format, missing parameters
2. **File System Errors**: Path not found, permission denied, disk full
3. **Network Errors**: Network path unavailable, timeout
4. **MCP Protocol Errors**: Connection issues, malformed requests

### Error Response Format
```json
{
  "error": {
    "code": "PATH_NOT_FOUND",
    "message": "Source path does not exist",
    "details": {
      "path": "H:\\FILES\\2_AUDIT DEPT\\Year 2025\\AC\\W\\AW001-AUD-***********",
      "input": "AW001-AUD-***********"
    }
  }
}
```

## Performance Optimization

### Strategies
1. **Async Operations**: All file operations are asynchronous
2. **Progress Streaming**: Real-time progress via SSE
3. **Memory Management**: Efficient directory traversal
4. **Caching**: Path resolution caching
5. **Parallel Processing**: Multiple inputs processed concurrently

### Resource Limits
- Maximum concurrent operations: 5
- SSE connection timeout: 30 minutes
- File operation timeout: 2 hours
- Memory limit per operation: 1GB
