/**
 * n8n-specific data formatters for workflow optimization
 */

export class N8nFormatters {
  constructor() {
    this.formatters = this.setupFormatters();
  }

  /**
   * Setup tool-specific formatters
   */
  setupFormatters() {
    return {
      analyze_audit_paths: this.formatAnalysisResult.bind(this),
      copy_audit_folders: this.formatCopyResult.bind(this),
      generate_long_names_report: this.formatLongNamesResult.bind(this),
      get_operation_status: this.formatStatusResult.bind(this)
    };
  }

  /**
   * Format result for n8n workflow consumption
   */
  formatResultForN8n(toolName, result, metadata) {
    const formatter = this.formatters[toolName];
    if (!formatter) {
      return this.formatGenericResult(result, metadata);
    }

    try {
      const formatted = formatter(result, metadata);
      return this.addN8nMetadata(formatted, metadata);
    } catch (error) {
      throw new Error(`Failed to format result for n8n: ${error.message}`);
    }
  }

  /**
   * Format analysis result for n8n
   */
  formatAnalysisResult(result, metadata) {
    const parsed = this.parseResult(result);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          // n8n-friendly structure
          success: true,
          data: {
            summary: this.formatSummaryForN8n(parsed.summary),
            results: this.formatAnalysisResultsForN8n(parsed.results),
            locations: this.extractLocationSummary(parsed.results),
            longNames: this.extractLongNamesSummary(parsed.results)
          },
          // n8n workflow data
          n8nData: {
            totalInputs: parsed.summary?.processedCount || 0,
            successfulInputs: parsed.summary?.successCount || 0,
            failedInputs: parsed.summary?.errorCount || 0,
            totalSizeMB: parsed.summary?.totalSizeInMB || 0,
            totalFiles: parsed.summary?.totalFiles || 0,
            totalLocationsFound: parsed.summary?.totalLocationsFound || 0,
            hasLongNames: this.hasLongNames(parsed.results),
            processingTime: metadata.executionTime,
            workflowReady: true
          }
        }, null, 2)
      }]
    };
  }

  /**
   * Format copy result for n8n
   */
  formatCopyResult(result, metadata) {
    const parsed = this.parseResult(result);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: true,
          data: {
            summary: parsed.summary,
            results: this.formatCopyResultsForN8n(parsed.results),
            operations: this.extractCopyOperations(parsed.results)
          },
          n8nData: {
            totalCopied: parsed.summary?.successCount || 0,
            totalFailed: parsed.summary?.errorCount || 0,
            totalSizeCopiedMB: parsed.summary?.totalCopiedSizeMB || 0,
            deletedOriginals: parsed.summary?.deletedOriginals || 0,
            processingTime: metadata.executionTime,
            copyOperations: this.extractCopyMetrics(parsed.results),
            workflowReady: true
          }
        }, null, 2)
      }]
    };
  }

  /**
   * Format long names report result for n8n
   */
  formatLongNamesResult(result, metadata) {
    const parsed = this.parseResult(result);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: true,
          data: {
            summary: parsed.summary,
            results: parsed.results,
            reports: this.extractReportFiles(parsed.results)
          },
          n8nData: {
            totalReports: this.countReports(parsed.results),
            totalViolations: this.countTotalViolations(parsed.results),
            reportFiles: this.extractReportFilesList(parsed.results),
            violationTypes: this.extractViolationTypes(parsed.results),
            processingTime: metadata.executionTime,
            workflowReady: true
          }
        }, null, 2)
      }]
    };
  }

  /**
   * Format status result for n8n
   */
  formatStatusResult(result, metadata) {
    const parsed = this.parseResult(result);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: true,
          data: parsed,
          n8nData: {
            hasActiveOperations: this.hasActiveOperations(parsed),
            operationCount: this.countOperations(parsed),
            processingTime: metadata.executionTime,
            workflowReady: true
          }
        }, null, 2)
      }]
    };
  }

  /**
   * Format generic result for n8n
   */
  formatGenericResult(result, metadata) {
    const parsed = this.parseResult(result);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: true,
          data: parsed,
          n8nData: {
            processingTime: metadata.executionTime,
            workflowReady: true
          }
        }, null, 2)
      }]
    };
  }

  /**
   * Add n8n metadata to formatted result
   */
  addN8nMetadata(formatted, metadata) {
    const content = JSON.parse(formatted.content[0].text);
    
    content.n8nMetadata = {
      executionId: metadata.executionId,
      executionTime: metadata.executionTime,
      memoryUsage: metadata.memoryUsage || process.memoryUsage(),
      timestamp: new Date().toISOString(),
      n8nContext: metadata.n8nContext || {},
      warnings: this.generateN8nWarnings(content.data),
      optimizations: this.generateN8nOptimizations(content.data)
    };

    return {
      content: [{
        type: 'text',
        text: JSON.stringify(content, null, 2)
      }]
    };
  }

  /**
   * Parse result from MCP response
   */
  parseResult(result) {
    if (typeof result === 'string') {
      try {
        return JSON.parse(result);
      } catch (error) {
        return { raw: result };
      }
    }
    
    if (result.content && Array.isArray(result.content) && result.content[0]?.text) {
      try {
        return JSON.parse(result.content[0].text);
      } catch (error) {
        return { raw: result.content[0].text };
      }
    }
    
    return result;
  }

  /**
   * Format summary for n8n consumption
   */
  formatSummaryForN8n(summary) {
    if (!summary) return {};
    
    return {
      processed: summary.processedCount || 0,
      successful: summary.successCount || 0,
      failed: summary.errorCount || 0,
      totalSizeMB: Math.round((summary.totalSizeInMB || 0) * 100) / 100,
      totalFiles: summary.totalFiles || 0,
      totalLocations: summary.totalLocationsFound || 0,
      completionRate: this.calculateCompletionRate(summary)
    };
  }

  /**
   * Format analysis results for n8n
   */
  formatAnalysisResultsForN8n(results) {
    if (!Array.isArray(results)) return [];
    
    return results.map(result => ({
      input: result.input,
      auditType: result.auditType,
      year: result.year,
      locationsFound: result.foundLocations?.length || 0,
      totalSizeMB: Math.round((result.totalSizeInMB || 0) * 100) / 100,
      totalFiles: result.totalFileCount || 0,
      hasLongNames: result.allLongNamesFiles?.length > 0,
      status: result.error ? 'failed' : 'success',
      error: result.error || null,
      // n8n-friendly location data
      locations: this.formatLocationsForN8n(result.foundLocations),
      // Simplified data for n8n processing
      n8nSimplified: {
        name: result.input,
        type: result.auditType,
        found: result.foundLocations?.length > 0,
        sizeMB: Math.round((result.totalSizeInMB || 0) * 100) / 100,
        files: result.totalFileCount || 0
      }
    }));
  }

  /**
   * Format copy results for n8n
   */
  formatCopyResultsForN8n(results) {
    if (!Array.isArray(results)) return [];
    
    return results.map(result => ({
      input: result.input,
      success: result.success,
      sourcePath: result.sourcePath,
      targetPath: result.targetPath,
      copiedFiles: result.copiedFiles || 0,
      copiedSizeMB: Math.round((result.copiedSizeMB || 0) * 100) / 100,
      originalDeleted: result.originalDeleted || false,
      durationMs: result.durationMs || 0,
      error: result.error || null,
      // n8n workflow data
      n8nSimplified: {
        name: result.input,
        copied: result.success,
        sizeMB: Math.round((result.copiedSizeMB || 0) * 100) / 100,
        timeMs: result.durationMs || 0
      }
    }));
  }

  /**
   * Format locations for n8n processing
   */
  formatLocationsForN8n(locations) {
    if (!Array.isArray(locations)) return [];
    
    return locations.map(location => ({
      type: location.type,
      path: location.sourcePath,
      sizeMB: Math.round((location.sizeInMB || 0) * 100) / 100,
      files: location.fileCount || 0,
      lastModified: location.lastModified,
      readable: location.permissions?.readable || false,
      writable: location.permissions?.writable || false,
      // Sample of full paths for n8n
      samplePaths: (location.fullPathsList || []).slice(0, 5)
    }));
  }

  /**
   * Extract location summary
   */
  extractLocationSummary(results) {
    const summary = {
      totalLocations: 0,
      locationTypes: {},
      averageLocationsPerInput: 0
    };

    if (!Array.isArray(results)) return summary;

    results.forEach(result => {
      if (result.foundLocations) {
        summary.totalLocations += result.foundLocations.length;
        
        result.foundLocations.forEach(location => {
          summary.locationTypes[location.type] = 
            (summary.locationTypes[location.type] || 0) + 1;
        });
      }
    });

    summary.averageLocationsPerInput = results.length > 0 ? 
      Math.round((summary.totalLocations / results.length) * 100) / 100 : 0;

    return summary;
  }

  /**
   * Extract long names summary
   */
  extractLongNamesSummary(results) {
    const summary = {
      totalReports: 0,
      inputsWithLongNames: 0,
      reportFiles: []
    };

    if (!Array.isArray(results)) return summary;

    results.forEach(result => {
      if (result.allLongNamesFiles && result.allLongNamesFiles.length > 0) {
        summary.inputsWithLongNames++;
        summary.totalReports += result.allLongNamesFiles.length;
        summary.reportFiles.push(...result.allLongNamesFiles);
      }
    });

    return summary;
  }

  /**
   * Extract copy operations summary
   */
  extractCopyOperations(results) {
    const operations = {
      successful: 0,
      failed: 0,
      totalFilesProcessed: 0,
      totalDataCopiedMB: 0,
      averageDurationMs: 0
    };

    if (!Array.isArray(results)) return operations;

    let totalDuration = 0;
    results.forEach(result => {
      if (result.success) {
        operations.successful++;
        operations.totalFilesProcessed += result.copiedFiles || 0;
        operations.totalDataCopiedMB += result.copiedSizeMB || 0;
      } else {
        operations.failed++;
      }
      totalDuration += result.durationMs || 0;
    });

    operations.totalDataCopiedMB = Math.round(operations.totalDataCopiedMB * 100) / 100;
    operations.averageDurationMs = results.length > 0 ? 
      Math.round(totalDuration / results.length) : 0;

    return operations;
  }

  /**
   * Extract copy metrics for n8n
   */
  extractCopyMetrics(results) {
    if (!Array.isArray(results)) return [];
    
    return results.map(result => ({
      input: result.input,
      files: result.copiedFiles || 0,
      sizeMB: Math.round((result.copiedSizeMB || 0) * 100) / 100,
      timeMs: result.durationMs || 0,
      success: result.success
    }));
  }

  /**
   * Helper methods
   */
  hasLongNames(results) {
    if (!Array.isArray(results)) return false;
    return results.some(result => 
      result.allLongNamesFiles && result.allLongNamesFiles.length > 0);
  }

  calculateCompletionRate(summary) {
    if (!summary || !summary.processedCount) return 0;
    return Math.round((summary.successCount / summary.processedCount) * 100);
  }

  hasActiveOperations(data) {
    if (data.analysis && Array.isArray(data.analysis)) {
      return data.analysis.length > 0;
    }
    if (data.fileOperations && Array.isArray(data.fileOperations)) {
      return data.fileOperations.length > 0;
    }
    return false;
  }

  countOperations(data) {
    let count = 0;
    if (data.analysis && Array.isArray(data.analysis)) {
      count += data.analysis.length;
    }
    if (data.fileOperations && Array.isArray(data.fileOperations)) {
      count += data.fileOperations.length;
    }
    return count;
  }

  countReports(results) {
    if (!Array.isArray(results)) return 0;
    return results.reduce((count, result) => 
      count + (result.allLongNamesFiles?.length || 0), 0);
  }

  countTotalViolations(results) {
    // This would need to be calculated based on the actual long names data
    // For now, return a placeholder
    return this.countReports(results);
  }

  extractReportFiles(results) {
    if (!Array.isArray(results)) return [];
    const files = [];
    results.forEach(result => {
      if (result.allLongNamesFiles) {
        files.push(...result.allLongNamesFiles);
      }
    });
    return files;
  }

  extractReportFilesList(results) {
    return this.extractReportFiles(results);
  }

  extractViolationTypes(results) {
    // Placeholder for violation type analysis
    return {
      nameViolations: 0,
      pathViolations: 0,
      bothViolations: 0
    };
  }

  /**
   * Generate n8n-specific warnings
   */
  generateN8nWarnings(data) {
    const warnings = [];

    // Check for large datasets
    if (data.summary?.totalFiles > 10000) {
      warnings.push('Large number of files detected - consider workflow timeout settings');
    }

    // Check for failed operations
    if (data.summary?.errorCount > 0) {
      warnings.push(`${data.summary.errorCount} operations failed - check error handling in workflow`);
    }

    // Check for long names
    if (data.longNames?.totalReports > 0) {
      warnings.push('Long file/path names detected - may cause issues in downstream nodes');
    }

    return warnings;
  }

  /**
   * Generate n8n optimization suggestions
   */
  generateN8nOptimizations(data) {
    const optimizations = [];

    // Suggest batch processing for large datasets
    if (data.results && data.results.length > 20) {
      optimizations.push('Consider using batch processing node for large input sets');
    }

    // Suggest error handling
    if (data.summary?.errorCount > 0) {
      optimizations.push('Add error handling nodes to manage failed operations');
    }

    // Suggest caching for repeated operations
    if (data.summary?.processingTime > 30000) {
      optimizations.push('Consider caching results for repeated workflow executions');
    }

    return optimizations;
  }
}
