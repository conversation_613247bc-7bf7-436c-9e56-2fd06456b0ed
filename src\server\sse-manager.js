/**
 * Server-Sent Events manager for real-time progress updates
 */

import { EventEmitter } from 'events';
import { SSE_EVENTS, DEFAULT_CONFIG } from '../shared/constants.js';

export class SSEManager extends EventEmitter {
  constructor(logger) {
    super();
    this.logger = logger;
    this.connections = new Map();
    this.heartbeatInterval = null;
    this.config = {
      heartbeatInterval: DEFAULT_CONFIG.HEARTBEAT_INTERVAL,
      maxConnections: DEFAULT_CONFIG.MAX_SSE_CONNECTIONS,
      connectionTimeout: DEFAULT_CONFIG.SSE_TIMEOUT
    };
    
    this.startHeartbeat();
  }

  /**
   * Add a new SSE connection
   * @param {string} sessionId - Unique session identifier
   * @param {object} response - HTTP response object
   * @param {object} request - HTTP request object
   */
  addConnection(sessionId, response, request) {
    // Check connection limit
    if (this.connections.size >= this.config.maxConnections) {
      response.status(429).json({ error: 'Too many connections' });
      return false;
    }

    // Set SSE headers
    response.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    const connection = {
      sessionId,
      response,
      request,
      connectedAt: Date.now(),
      lastHeartbeat: Date.now(),
      isActive: true
    };

    this.connections.set(sessionId, connection);
    this.logger.info(`SSE connection added: ${sessionId} (${this.connections.size} total)`);

    // Send initial connection event
    this.sendEvent(sessionId, {
      type: SSE_EVENTS.START,
      data: {
        sessionId,
        timestamp: new Date().toISOString(),
        message: 'Connected to audit path analyzer'
      }
    });

    // Handle client disconnect
    request.on('close', () => {
      this.removeConnection(sessionId);
    });

    request.on('error', (error) => {
      this.logger.error(`SSE connection error for ${sessionId}:`, error);
      this.removeConnection(sessionId);
    });

    // Set connection timeout
    setTimeout(() => {
      if (this.connections.has(sessionId)) {
        this.logger.info(`SSE connection timeout for ${sessionId}`);
        this.removeConnection(sessionId);
      }
    }, this.config.connectionTimeout);

    this.emit('connectionAdded', { sessionId, totalConnections: this.connections.size });
    return true;
  }

  /**
   * Remove an SSE connection
   * @param {string} sessionId - Session identifier to remove
   */
  removeConnection(sessionId) {
    const connection = this.connections.get(sessionId);
    if (connection) {
      connection.isActive = false;
      
      try {
        connection.response.end();
      } catch (error) {
        // Connection already closed
      }
      
      this.connections.delete(sessionId);
      this.logger.info(`SSE connection removed: ${sessionId} (${this.connections.size} remaining)`);
      
      this.emit('connectionRemoved', { sessionId, totalConnections: this.connections.size });
    }
  }

  /**
   * Send an event to a specific session
   * @param {string} sessionId - Target session ID
   * @param {object} event - Event object with type and data
   */
  sendEvent(sessionId, event) {
    const connection = this.connections.get(sessionId);
    if (!connection || !connection.isActive) {
      return false;
    }

    try {
      const eventData = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        ...event
      };

      const sseData = this.formatSSEData(eventData);
      connection.response.write(sseData);
      
      connection.lastHeartbeat = Date.now();
      return true;
    } catch (error) {
      this.logger.error(`Failed to send SSE event to ${sessionId}:`, error);
      this.removeConnection(sessionId);
      return false;
    }
  }

  /**
   * Broadcast an event to all connected sessions
   * @param {object} event - Event object to broadcast
   */
  broadcastToAll(event) {
    const sessionIds = Array.from(this.connections.keys());
    const results = sessionIds.map(sessionId => this.sendEvent(sessionId, event));
    
    const successCount = results.filter(Boolean).length;
    this.logger.debug(`Broadcasted event to ${successCount}/${sessionIds.length} connections`);
    
    return successCount;
  }

  /**
   * Broadcast an event to a specific session (alias for sendEvent)
   * @param {string} sessionId - Target session ID
   * @param {object} event - Event object
   */
  broadcast(sessionId, event) {
    return this.sendEvent(sessionId, event);
  }

  /**
   * Send progress update to a session
   * @param {string} sessionId - Target session ID
   * @param {object} progressData - Progress information
   */
  sendProgress(sessionId, progressData) {
    return this.sendEvent(sessionId, {
      type: SSE_EVENTS.PROGRESS,
      data: progressData
    });
  }

  /**
   * Send completion event to a session
   * @param {string} sessionId - Target session ID
   * @param {object} resultData - Completion result data
   */
  sendComplete(sessionId, resultData) {
    return this.sendEvent(sessionId, {
      type: SSE_EVENTS.COMPLETE,
      data: resultData
    });
  }

  /**
   * Send error event to a session
   * @param {string} sessionId - Target session ID
   * @param {object} errorData - Error information
   */
  sendError(sessionId, errorData) {
    return this.sendEvent(sessionId, {
      type: SSE_EVENTS.ERROR,
      data: errorData
    });
  }

  /**
   * Format data for SSE transmission
   * @param {object} eventData - Event data to format
   * @returns {string} - Formatted SSE data
   */
  formatSSEData(eventData) {
    const lines = [];
    
    if (eventData.id) {
      lines.push(`id: ${eventData.id}`);
    }
    
    if (eventData.type) {
      lines.push(`event: ${eventData.type}`);
    }
    
    // Handle data field
    if (eventData.data !== undefined) {
      const dataStr = typeof eventData.data === 'string' 
        ? eventData.data 
        : JSON.stringify(eventData.data);
      
      // Split multi-line data
      dataStr.split('\n').forEach(line => {
        lines.push(`data: ${line}`);
      });
    }
    
    // Add retry field for reconnection
    if (eventData.retry) {
      lines.push(`retry: ${eventData.retry}`);
    }
    
    return lines.join('\n') + '\n\n';
  }

  /**
   * Start heartbeat to keep connections alive
   */
  startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, this.config.heartbeatInterval);

    this.logger.info(`SSE heartbeat started (${this.config.heartbeatInterval}ms interval)`);
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      this.logger.info('SSE heartbeat stopped');
    }
  }

  /**
   * Send heartbeat to all connections
   */
  sendHeartbeat() {
    const now = Date.now();
    const staleConnections = [];

    for (const [sessionId, connection] of this.connections) {
      // Check for stale connections
      if (now - connection.lastHeartbeat > this.config.heartbeatInterval * 2) {
        staleConnections.push(sessionId);
        continue;
      }

      // Send heartbeat
      this.sendEvent(sessionId, {
        type: SSE_EVENTS.HEARTBEAT,
        data: { timestamp: new Date().toISOString() }
      });
    }

    // Remove stale connections
    staleConnections.forEach(sessionId => {
      this.logger.warn(`Removing stale SSE connection: ${sessionId}`);
      this.removeConnection(sessionId);
    });
  }

  /**
   * Get connection statistics
   * @returns {object} - Connection statistics
   */
  getStats() {
    const now = Date.now();
    const connections = Array.from(this.connections.values());
    
    return {
      totalConnections: connections.length,
      activeConnections: connections.filter(c => c.isActive).length,
      averageConnectionDuration: connections.length > 0 
        ? Math.round(connections.reduce((sum, c) => sum + (now - c.connectedAt), 0) / connections.length)
        : 0,
      oldestConnection: connections.length > 0 
        ? Math.min(...connections.map(c => c.connectedAt))
        : null
    };
  }

  /**
   * Get all active session IDs
   * @returns {string[]} - Array of active session IDs
   */
  getActiveSessions() {
    return Array.from(this.connections.keys());
  }

  /**
   * Check if a session is connected
   * @param {string} sessionId - Session ID to check
   * @returns {boolean} - True if session is connected
   */
  isConnected(sessionId) {
    const connection = this.connections.get(sessionId);
    return connection && connection.isActive;
  }

  /**
   * Close all connections and cleanup
   */
  cleanup() {
    this.logger.info('Cleaning up SSE manager...');
    
    // Stop heartbeat
    this.stopHeartbeat();
    
    // Close all connections
    const sessionIds = Array.from(this.connections.keys());
    sessionIds.forEach(sessionId => this.removeConnection(sessionId));
    
    // Remove all listeners
    this.removeAllListeners();
    
    this.logger.info('SSE manager cleanup completed');
  }
}
